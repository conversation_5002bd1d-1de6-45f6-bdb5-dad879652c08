import { ActivityType } from 'discord.js';
import dataManager from '../utils/dataManager';
import logger from '../utils/logger';

export const name = 'ready';
export const once = true;
export const execute = async client => {
  logger.info(`Logged in as ${client.user.tag}!`);
  logger.info(`Bo<PERSON> is ready to serve in ${client.guilds.cache.size} guild(s)`);

  // Set default activity
  client.user.setPresence({
    activities: [
      {
        name: 'temporary voice channels',
        type: ActivityType.Watching,
      },
    ],
    status: 'online',
  });

  // Preload all guild prefixes for immediate access
  try {
    await dataManager.preloadGuildPrefixes();
    logger.info('Guild prefixes preloaded for real-time access');
  } catch (err) {
    logger.error('Error preloading guild prefixes:', err);
  }

  // Load temporary channels from storage
  try {
    // Use loadTempChannels function from index.js
    global.loadTempChannels();

    // Run startup cleanup to check for and delete empty channels
    logger.info('Running startup cleanup for voice channels...');
    await global.startupCleanup();
  } catch (error) {
    logger.error('Error during startup cleanup:', error);
  }

  // Check for blacklisted servers
  const blacklist: any = dataManager.loadBlacklistedServers();
  const blacklistedIds = Object.keys(blacklist.blacklistedServers || {});

  if (blacklistedIds.length > 0) {
    logger.info(`Found ${blacklistedIds.length} blacklisted server(s)`);

    let leftCount = 0;

    // Leave any blacklisted servers
    for (const serverId of blacklistedIds) {
      const guild = client.guilds.cache.get(serverId);

      if (guild) {
        const blacklistInfo = blacklist.blacklistedServers[serverId];
        logger.warn(
          `Leaving blacklisted server: ${guild.name} (${guild.id}), reason: ${blacklistInfo.reason}`
        );

        try {
          await guild.leave();
          leftCount++;
        } catch (error) {
          logger.error(`Failed to leave blacklisted server ${guild.id}:`, error);
        }
      }
    }

    if (leftCount > 0) {
      logger.info(`Left ${leftCount} blacklisted server(s)`);
    }
  }
};
