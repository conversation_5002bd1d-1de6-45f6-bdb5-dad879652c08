import { ActionRowBuilder, StringSelectMenuBuilder } from 'discord.js';
import logger from '../utils/logger';
import { replyOrFollowUpEphemeral, handleInteractionError } from '../utils/interactionUtils';
import dataManager from '../utils/dataManager';
import { EMOJI } from '../constants/emoji';

export const name = 'privacy';

// Called when the "Privacy" button is clicked
// Interaction is deferred, ownership & cooldown checked in interactionCreate.ts
export const execute = async (interaction, client, userChannel) => {
  // Sends the select menu to the user
  try {
    // Determine current state for default value
    let currentState = 'public';
    const everyonePerms = userChannel.permissionsFor(interaction.guild.roles.everyone);
    if (!everyonePerms.has('ViewChannel')) {
      currentState = 'hide';
    } else if (!everyonePerms.has('Connect')) {
      currentState = 'lock';
    }

    const privacySelect = new StringSelectMenuBuilder()
      .setCustomId(`privacy_select_${userChannel.id}`) // Consistent ID format
      .setPlaceholder('Select channel privacy')
      .addOptions([
        {
          label: 'Public',
          description: 'Everyone can see and join.',
          value: 'public',
          default: currentState === 'public',
          emoji: EMOJI[client.user.id].PUBLIC,
        },
        {
          label: 'Locked',
          description: 'Everyone can see, only trusted can join.',
          value: 'lock',
          default: currentState === 'lock',
          emoji: EMOJI[client.user.id].LOCKED,
        },
        {
          label: 'Hidden',
          description: 'Only you and trusted users can see and join.',
          value: 'hide',
          default: currentState === 'hide',
          emoji: EMOJI[client.user.id].HIDDEN,
        },
      ]);

    const row: any = new ActionRowBuilder().addComponents(privacySelect);

    // Use replyOrFollowUpEphemeral as interaction is deferred by the central handler
    await replyOrFollowUpEphemeral(interaction, {
      content: 'Choose the privacy setting for your channel:',
      components: [row],
    });
  } catch (error) {
    logger.error(`Error sending privacy select menu: ${error.message}`);
    await handleInteractionError('privacy.execute', error, interaction, client);
  }
};

// Called when an option is selected from the menu sent above
export const handleSelectMenu = async (interaction, client, targetChannel) => {
  const menuSource = `privacy.handleSelectMenu:selectMenu:${interaction.customId}`;
  // Cooldown & ownership checked in interactionCreate.ts before calling this
  if (!interaction.isStringSelectMenu()) return; // Type guard

  try {
    const selectedValue = interaction.values[0];
    const userId = interaction.user.id;
    const everyoneRole = interaction.guild.roles.everyone;
    logger.debug(
      `${menuSource}: User ${userId} selected privacy "${selectedValue}" for channel ${targetChannel.id}`
    );

    let replyMessage = '';

    // Fetch settings for trusted users list (assuming async)
    const userSettings = (await dataManager.getUserSettings(userId)) || { trustedUsers: [] };
    const trustedUsers = userSettings.trustedUsers || [];

    // Reset everyone perms first
    await targetChannel.permissionOverwrites.edit(
      everyoneRole,
      {
        ViewChannel: null,
        Connect: null,
      },
      `Privacy reset by owner ${userId}`
    );

    switch (selectedValue) {
      case 'public':
        await targetChannel.permissionOverwrites.edit(
          everyoneRole,
          {
            ViewChannel: true,
            Connect: true,
          },
          `Set to Public by owner ${userId}`
        );
        replyMessage = `${EMOJI[client.user.id].CHECK} Channel is now public.`;
        break;

      case 'lock':
        await targetChannel.permissionOverwrites.edit(
          everyoneRole,
          {
            ViewChannel: true,
            Connect: false,
          },
          `Set to Locked by owner ${userId}`
        );
        // Ensure owner & trusted can connect
        await targetChannel.permissionOverwrites.edit(userId, { Connect: true });
        for (const trustedId of trustedUsers) {
          await targetChannel.permissionOverwrites
            .edit(trustedId, { Connect: true })
            .catch(err =>
              logger.warn(
                `Failed to grant Connect to trusted user ${trustedId} in locked channel ${targetChannel.id}: ${err.message}`
              )
            );
        }
        replyMessage = `${EMOJI[client.user.id].CHECK} Channel locked.`;
        break;

      case 'hide':
        await targetChannel.permissionOverwrites.edit(
          everyoneRole,
          {
            ViewChannel: false,
            Connect: false,
          },
          `Set to Hidden by owner ${userId}`
        );
        // Ensure owner & trusted can see/connect
        await targetChannel.permissionOverwrites.edit(userId, { ViewChannel: true, Connect: true });
        for (const trustedId of trustedUsers) {
          await targetChannel.permissionOverwrites
            .edit(trustedId, { ViewChannel: true, Connect: true })
            .catch(err =>
              logger.warn(
                `Failed to grant View/Connect to trusted user ${trustedId} in hidden channel ${targetChannel.id}: ${err.message}`
              )
            );
        }
        replyMessage = `${EMOJI[client.user.id].CHECK} Channel hidden.`;
        break;

      default:
        logger.warn(`${menuSource}: Invalid privacy option selected: ${selectedValue}`);
        await replyOrFollowUpEphemeral(interaction, {
          content: `${EMOJI[client.user.id].CROSS} Invalid option.`,
        });
        return;
    }

    logger.info(`Channel ${targetChannel.id} privacy set to "${selectedValue}" by ${userId}`);

    // Final reply handled here
    await replyOrFollowUpEphemeral(interaction, { content: replyMessage, components: [] });
  } catch (error) {
    logger.error(`${menuSource}: Failed to handle privacy select menu: ${error.message}`);
    await handleInteractionError(menuSource, error, interaction, client);
  }
};
