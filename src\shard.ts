/**
 * MyVC Discord Bot - Sharding Manager
 * Handles the creation and management of multiple bot shards
 * for improved performance and stability with many guilds
 */
import 'dotenv/config';
import { ShardingManager } from 'discord.js';
import path from 'path';
import logger from './utils/logger';
import dataManager from './utils/dataManager';

// Number of shards to spawn
// 'auto' = Discord.js will automatically decide based on guild count
// You can also set a specific number like 2, 4, etc.
const SHARD_COUNT =
  process.env.SHARD_COUNT === 'auto'
    ? 'auto'
    : process.env.SHARD_COUNT
      ? parseInt(process.env.SHARD_COUNT, 10)
      : 'auto';

// Path to the main bot file - must point to the JavaScript file (not TypeScript)
// Note: When compiled with outDir: "dist", index.ts becomes dist/src/index.js
const SHARD_FILE = path.join(__dirname, 'index.js');

// If running from TypeScript source, use ts-node path
// If running from compiled JavaScript, use the relative path
if (__filename.includes('dist/src/shard.js')) {
  // We're running from the compiled JavaScript
  logger.debug('Running from compiled JavaScript, adjusting shard file path');
  // When running from dist/src/shard.js, index.js is in the same directory
}

/**
 * Initialize the database connection before starting shards
 */
async function initDatabase() {
  try {
    logger.info('Connecting to database before spawning shards...');
    await dataManager.connectToMongoDB();
    logger.info('Database connection successful. Ready to spawn shards.');
    return true;
  } catch (error) {
    logger.error('Failed to connect to database:', error);
    return false;
  }
}

/**
 * Create and configure the sharding manager
 */
async function initShardManager() {
  // First connect to the database
  const dbInitialized = await initDatabase();
  if (!dbInitialized) {
    logger.error('Cannot initialize shards without database connection. Exiting.');
    process.exit(1);
  }

  // Create the sharding manager
  const manager = new ShardingManager(SHARD_FILE, {
    token: process.env.TOKEN,
    totalShards: SHARD_COUNT,
    respawn: true,
    shardArgs: process.argv.slice(2), // Pass command line arguments to shards
    execArgv: process.execArgv, // Pass Node.js arguments to shards
  });

  // Log shard events
  manager.on('shardCreate', shard => {
    logger.info(`Launched shard ${shard.id}`);

    // Listen for shard ready/disconnect events
    shard.on('ready', () => {
      logger.info(`Shard ${shard.id} ready`);
    });

    shard.on('disconnect', () => {
      logger.warn(`Shard ${shard.id} disconnected`);
    });

    shard.on('reconnecting', () => {
      logger.info(`Shard ${shard.id} reconnecting`);
    });

    shard.on('death', childProcess => {
      // Check if it's a ChildProcess (which has exitCode) rather than a Worker
      const exitCodeInfo = childProcess.hasOwnProperty('exitCode')
        ? `with exit code ${(childProcess as any).exitCode}`
        : 'unexpectedly';
      logger.error(`Shard ${shard.id} died ${exitCodeInfo}`);
    });

    shard.on('error', error => {
      logger.error(`Shard ${shard.id} encountered an error:`, error);
    });
  });

  // Collect stats from all shards periodically
  setInterval(
    async () => {
      try {
        // Get the guild counts from each shard
        const guildCounts = await manager.fetchClientValues('guilds.cache.size');
        const totalGuilds = guildCounts.reduce((acc: number, count: number) => acc + count, 0);

        // Get voice connection counts
        const voiceConnectionCounts = await manager.broadcastEval(client => {
          return client.tempChannels?.size || 0;
        });
        const totalVoiceConnections = voiceConnectionCounts.reduce(
          (acc: number, count: number) => acc + count,
          0
        );

        logger.info(`Total guilds: ${totalGuilds} across ${guildCounts.length} shards`);
        logger.info(`Active voice channels: ${totalVoiceConnections}`);

        // Save stats to database if needed
        await dataManager.saveData('shard_stats', {
          timestamp: new Date(),
          shardCount: guildCounts.length,
          totalGuilds,
          totalVoiceConnections,
          shardGuildCounts: guildCounts,
        });
      } catch (error) {
        logger.error('Failed to fetch shard statistics:', error);
      }
    },
    5 * 60 * 1000
  ); // Every 5 minutes

  return manager;
}

// Start the sharding manager
initShardManager()
  .then(manager => {
    logger.info(`Starting MyVC bot with ${SHARD_COUNT} shards`);

    // Spawn all shards
    manager
      .spawn({
        timeout: 60000, // 60 seconds timeout for spawning
        delay: 7500, // 7.5 seconds delay between shard spawns to avoid rate limits
      })
      .catch(error => {
        logger.error('Failed to spawn shards:', error);
        process.exit(1);
      });
  })
  .catch(error => {
    logger.error('Failed to initialize sharding manager:', error);
    process.exit(1);
  });

// Handle process shutdown
process.on('SIGINT', handleShutdown);
process.on('SIGTERM', handleShutdown);

/**
 * Clean shutdown handler
 */
async function handleShutdown() {
  logger.info('Shutting down sharding manager...');

  try {
    // Clean up database connection
    await dataManager.cleanup();
    logger.info('Database connections closed.');

    // Exit cleanly
    process.exit(0);
  } catch (error) {
    logger.error('Error during shutdown:', error);
    process.exit(1);
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', error => {
  logger.error('Uncaught Exception in Sharding Manager:', error);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', reason => {
  const errorMessage = 'Unhandled Rejection in Sharding Manager';

  if (reason instanceof Error) {
    logger.error(errorMessage, reason);
  } else if (typeof reason === 'string') {
    logger.error(`${errorMessage}: ${reason}`);
  } else {
    // Convert unknown type to string representation
    logger.error(`${errorMessage}: ${String(reason)}`);
  }
});
