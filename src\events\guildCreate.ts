/**
 * Guild Create Event
 *
 * Triggered when the bot joins a new server.
 * Provides setup instructions for the MyVC system.
 * Logs detailed information about the guild join event.
 */
import {
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  EmbedBuilder,
  Events,
  PermissionFlagsBits,
} from 'discord.js';
import logger from '../utils/logger';
import dataManager from '../utils/dataManager';
import guildLogger from '../utils/guildLogger';
import { EMOJI } from '../constants/emoji';

export const name = Events.GuildCreate;
export const once = false;
export const execute = async (guild, client) => {
  try {
    // Log the guild join event with detailed information
    guildLogger.logGuildJoin(guild);

    // Check if this guild is blacklisted
    const blacklistInfo = await dataManager.isServerBlacklisted(guild.id);

    if (blacklistInfo) {
      logger.warn(
        `Joined blacklisted server: ${guild.name} (${guild.id}), reason: ${blacklistInfo.reason}`
      );
      logger.warn(`Leaving blacklisted server: ${guild.name} (${guild.id})`);

      // Leave the guild if it's blacklisted
      await guild.leave();
      // Log the leave event with the blacklist reason
      guildLogger.logGuildLeave(guild, `blacklisted:${blacklistInfo.reason}`);
      return;
    }

    // Check if this guild already has settings
    const guildSettings = await dataManager.loadGuildSettings();
    const hasSettings =
      guildSettings[guild.id] &&
      guildSettings[guild.id].joinChannelId &&
      guildSettings[guild.id].tempCategoryId;

    // If the guild already has settings, no need to send setup instructions
    if (hasSettings) {
      logger.info(`Guild ${guild.id} already has MyVC settings configured`);
      // return;
    }

    // Try to find a suitable channel to send setup instructions
    // Fetch all channels of the guild
    const channels = await guild.channels.fetch();

    // Loop through and log the names and types of the channels
    channels.forEach(channel => {
      console.log(`[${channel.type}] ${channel.name} (${channel.id})`);
    });

    // Optional: Send a welcome message to the first text channel the bot can send to
    const defaultChannel = channels.find(
      ch => ch.isTextBased() && ch.permissionsFor(guild.members.me).has('SendMessages')
    );

    if (defaultChannel) {
      const row1 = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
          .setLabel('Setup')
          .setEmoji(EMOJI[client.user.id].SETUP)
          .setCustomId('start_setup')
          .setStyle(ButtonStyle.Secondary)
      );

      // Send welcome message with setup instructions
      const embed = new EmbedBuilder()
        .setTitle('👋 Thanks for adding MyVC Bot to your server!')
        .setDescription(
          `
To get started, run the \`/setup\` command to create a temporary voice channel system.

This will create:
- A **MyVC Category**
- A **JOIN_TO_CREATE** channel (join this to create your own voice channel)
- An **interface** channel with buttons to customize your temporary channels

🔒 **Note:** Only server administrators can run the \`/setup\` command.

❓ Need help? Check out the documentation or join our support server!
  `.trim()
        )
        .setColor(0x5865f2); // Optional: Use a Discord blurple color

      await defaultChannel.send({ embeds: [embed], components: [row1] });

      logger.info(
        `Sent setup instructions to ${defaultChannel.name} (${defaultChannel.id}) in ${guild.name}`
      );
    } else {
      logger.warn(
        `Could not find suitable channel to send setup instructions in ${guild.name} (${guild.id})`
      );
    }
  } catch (error) {
    logger.error(`Error handling guild join for ${guild.id}:`, error);
  }
};
