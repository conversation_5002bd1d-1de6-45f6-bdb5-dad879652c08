import logger from '../utils/logger';
import { replyOrFollowUpEphemeral, handleInteractionError } from '../utils/interactionUtils';
import { EMOJI } from '../constants/emoji';
import { GuildMember, VoiceChannel, EmbedBuilder } from 'discord.js';
import dataManager from '../utils/dataManager';

export const name = 'claim';

// Assume this map exists and is updated by voiceStateUpdate
// Map<channelId, lastSeenTimestamp>
// const tempChannelLastSeen: Map<string, number> = client.tempChannelLastSeen;

export const execute = async (interaction, client) => {
  const claimSource = `claim.execute:button:${interaction.customId}`;
  const guild = interaction.guild;
  const member = interaction.member as GuildMember;
  const userId = interaction.user.id;

  // We'll need this later, assuming it exists on the client
  const tempChannelLastSeen: Map<string, number> = client.tempChannelLastSeen || new Map(); // Use existing or empty map

  if (!guild || !member) {
    await replyOrFollowUpEphemeral(interaction, {
      content: `${EMOJI[client.user.id].CROSS} This command can only be used in a server.`,
    });
    return;
  }

  if (!member.voice.channel) {
    await replyOrFollowUpEphemeral(interaction, {
      content: `${EMOJI[client.user.id].CROSS} You need to be in a voice channel to claim it.`,
    });
    return;
  }

  const targetChannel = member.voice.channel as VoiceChannel;

  if (!client.tempChannels.has(targetChannel.id)) {
    await replyOrFollowUpEphemeral(interaction, {
      content: `${EMOJI[client.user.id].CROSS} This channel is not a claimable temporary channel.`,
    });
    return;
  }

  const currentOwnerId = client.tempChannels.get(targetChannel.id);
  let canClaim = false;
  let previousOwnerId: string | null = null; // Keep track if we are overwriting ownership

  if (currentOwnerId) {
    previousOwnerId = currentOwnerId; // Store the ID of the owner being potentially replaced
    const ownerInChannel = targetChannel.members.has(currentOwnerId);

    if (ownerInChannel) {
      // Owner is currently in the channel, cannot claim
      const ownerMember = targetChannel.members.get(currentOwnerId); // Fetch from channel members for efficiency
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].CROSS} This channel is already owned by ${ownerMember?.user?.tag || 'someone'} who is currently in the channel.`,
      });
      return; // Stop execution
    } else {
      // Owner exists but is not in the channel
      const ownerMember = await guild.members.fetch(currentOwnerId).catch(() => null);
      if (!ownerMember) {
        // Owner left the guild or is invalid, allow claim
        logger.warn(
          `${claimSource}: Channel ${targetChannel.id} owner ${currentOwnerId} not found in guild. Allowing claim by ${userId}.`
        );
        canClaim = true;
      } else {
        // Owner exists in guild but not in channel, check last seen time
        const lastSeen = tempChannelLastSeen.get(targetChannel.id);
        const ownerAbsentDuration = lastSeen ? Date.now() - lastSeen : Infinity; // Treat no record as infinitely absent
        const ONE_MINUTE = 60 * 1000;

        if (ownerAbsentDuration > ONE_MINUTE) {
          logger.info(
            `${claimSource}: Channel ${targetChannel.id} owner ${currentOwnerId} has been absent for ${ownerAbsentDuration / 1000}s (> 60s). Allowing claim by ${userId}.`
          );
          canClaim = true;
        } else {
          // Owner absent for less than 1 minute
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].CROSS} The owner (${ownerMember.user.tag}) has only been absent for ${Math.round(ownerAbsentDuration / 1000)} seconds. Please wait at least 1 minute before claiming.`,
          });
          return; // Stop execution
        }
      }
    }
  } else {
    // Channel is unowned, allow claim
    canClaim = true;
  }

  // --- Claim the Channel ---
  if (canClaim) {
    try {
      logger.info(
        `User ${interaction.user.tag} (${userId}) is claiming channel ${targetChannel.name} (${targetChannel.id}) ${previousOwnerId ? `from previous owner ${previousOwnerId}` : ''}`
      );

      // --- Reset Old Config / Permissions ---
      if (previousOwnerId) {
        try {
          // Remove previous owner's specific permissions
          await targetChannel.permissionOverwrites.delete(
            previousOwnerId,
            `Channel claimed by ${interaction.user.tag}`
          );
          logger.info(
            `${claimSource}: Removed permission overwrites for previous owner ${previousOwnerId} on channel ${targetChannel.id}.`
          );
        } catch (permError) {
          // Log if deleting old perms fails, but proceed with claim
          logger.warn(
            `${claimSource}: Failed to remove permissions for previous owner ${previousOwnerId} on ${targetChannel.id}: ${permError.message}`
          );
        }
      }

      // Update the owner in the map
      client.tempChannels.set(targetChannel.id, userId);
      // Update/set the last seen time for the new owner (or clear it as they are now present)
      tempChannelLastSeen.delete(targetChannel.id); // Clear timestamp since new owner is present

      // Reset channel to default settings - clear old owner's configuration
      try {
        // Reset to default channel settings
        logger.info(
          `${claimSource}: Resetting channel ${targetChannel.id} to default configuration`
        );

        // Reset name to a basic format if needed
        const defaultName = `${member.user.username}'s channel`;
        await targetChannel.setName(defaultName);

        // Reset other channel settings to defaults
        await targetChannel.setUserLimit(0); // No user limit
        await targetChannel.setBitrate(64000); // Default bitrate
        // Reset any other customizable settings

        logger.info(`${claimSource}: Successfully reset channel configuration to defaults`);
      } catch (resetError) {
        logger.error(
          `${claimSource}: Failed to reset channel configuration: ${resetError.message}`
        );
        // Non-critical, continue with the claim
      }

      // Fetch the new owner's configuration from MongoDB
      let newOwnerConfig = null;
      try {
        // Use dataManager instead of client.db
        newOwnerConfig = await dataManager.getUserSettings(userId);
        logger.info(`${claimSource}: Retrieved config for new owner ${userId}`);
      } catch (configError) {
        logger.error(`${claimSource}: Failed to retrieve new owner config: ${configError.message}`);
        // Continue with default settings if config retrieval fails
      }

      // Grant ownership permissions to the new owner
      await targetChannel.permissionOverwrites.edit(
        userId,
        {
          ViewChannel: true,
          Connect: true,
          Speak: true,
          // Stream: true,
          // MuteMembers: true,
          // DeafenMembers: true,
          // MoveMembers: true,
          // UseEmbeddedActivities: true,
          // ManageChannels: true, // Allow owner to manage channel (rename, limit, etc.)
          // ManageRoles: true, // Needed to manage permissions for privacy/trust/block
        },
        { reason: `Claimed by ${interaction.user.tag}` } // Use reason object for clarity
      );

      // Apply any new owner's config settings to the channel
      if (newOwnerConfig) {
        try {
          // Apply channel name format if configured
          if (newOwnerConfig.channelNameFormat) {
            const formattedName = newOwnerConfig.channelNameFormat
              .replace('{username}', member.user.username)
              .replace('{tag}', member.user.tag);
            // Add other replacements as needed

            await targetChannel.setName(formattedName);
            logger.info(`${claimSource}: Updated channel name to ${formattedName}`);
          }

          // Apply other channel settings from the new owner's config
          if (newOwnerConfig.userLimit !== undefined) {
            await targetChannel.setUserLimit(newOwnerConfig.userLimit);
          }

          if (newOwnerConfig.bitrate !== undefined) {
            await targetChannel.setBitrate(newOwnerConfig.bitrate);
          }

          // Apply any other custom settings from the config
          // This will depend on what settings your app supports

          logger.info(
            `${claimSource}: Applied new owner's settings to channel ${targetChannel.id}`
          );
        } catch (settingsError) {
          logger.error(`${claimSource}: Failed to apply new settings: ${settingsError.message}`);
          // Non-critical, continue
        }
      }

      // Update MongoDB with the new owner information
      try {
        // Use dataManager to update channel owner
        // Save the channel information to the database
        await dataManager.saveChannelSettings({
          [targetChannel.id]: {
            ownerId: userId,
            guildId: guild.id,
            lastUpdated: new Date(),
          },
        });
        logger.info(
          `${claimSource}: Updated channel owner in database for channel ${targetChannel.id}`
        );
      } catch (dbError) {
        logger.error(
          `${claimSource}: Failed to update database for channel ${targetChannel.id}: ${dbError.message}`
        );
        // Continue with the claim even if DB update fails, but log the error
      }

      const successEmbed = new EmbedBuilder()
        .setColor('#00FF00')
        .setTitle(`${EMOJI[client.user.id].CHECK} Channel Claimed`)
        .setDescription(`You have successfully claimed the channel **${targetChannel.name}**!`)
        .setTimestamp();

      await replyOrFollowUpEphemeral(interaction, {
        embeds: [successEmbed],
      });
    } catch (error) {
      logger.error(
        `${claimSource}: Failed to claim channel ${targetChannel.id} for user ${userId}: ${error.message}`
      );
      // Attempt to revert ownership map if claim failed mid-process
      if (client.tempChannels.get(targetChannel.id) === userId) {
        client.tempChannels.set(targetChannel.id, previousOwnerId); // Revert to previous owner (or null if none)
        // Should we also restore previousOwner's permissions? More complex.
      }
      await handleInteractionError(claimSource, error, interaction, client);
    }
  }
};
