/**
 * Data Manager - Handles persistent storage for the MyVC Bot
 * MongoDB-based data management system
 */
import { EventEmitter } from 'events';
import mongoose from 'mongoose';
import MongoConnection from '../data/connection';
import {
  blacklistedServerRepository,
  channelRepository,
  guildRepository,
  tempChannelRepository,
  userRepository,
} from '../data/index';
import GuildSettingsModel, { DEFAULT_GUILD_SETTINGS } from '../data/models/guild';
import logger from './logger';

class DataManager extends EventEmitter {
  [x: string]: any;
  private autoSaveInterval: number;
  private maxRetries: number;
  private retryDelay: number;
  private dirtyFlags: Set<string>;
  private autoSaveTimer: NodeJS.Timeout | null;
  private validationInterval: NodeJS.Timeout | null;
  private mongoEnabled: boolean = false;
  private connectionAttemptInProgress: boolean = false;
  private connectionReady: boolean = false;
  private pendingOperations: Map<string, any[]> = new Map();
  private cache: Map<string, { data: any; timestamp: number }> = new Map();
  private cacheTTL: number = 10 * 1000; // Reduced from 60 seconds to 10 seconds

  /**
   * Initialize the data manager
   * @param {Object} options - Configuration options
   */
  constructor(options: any = {}) {
    super(); // Initialize EventEmitter

    // Auto-save interval (reduced to 1 minute)
    this.autoSaveInterval = options.autoSaveInterval || 1 * 1000;

    // Retry configuration
    this.maxRetries = options.maxRetries || 2;
    this.retryDelay = options.retryDelay || 3000;

    // Track dirty state for each data type
    this.dirtyFlags = new Set();

    // Start auto-save timer
    // Start periodic validation
    this.startPeriodicValidation();
  }

  /**
   * Check if the MongoDB connection is established and ready.
   */
  get isReady(): boolean {
    return this.mongoEnabled && this.connectionReady;
  }

  /**
   * Connect to MongoDB and return a Promise that resolves on success or rejects on initial failure.
   * Handles retries internally but resolves/rejects the returned promise based on the first attempt's outcome or eventual success.
   */
  public connectToMongoDB(): Promise<void> {
    // Return a new promise that resolves/rejects based on connection status
    return new Promise(async (resolve, reject) => {
      if (this.isReady) {
        logger.debug('MongoDB already connected.');
        resolve();
        return;
      }

      if (this.connectionAttemptInProgress) {
        logger.debug('Connection attempt already in progress, waiting...');
        // Simple wait mechanism: check status periodically
        const waitInterval = setInterval(() => {
          if (this.isReady) {
            clearInterval(waitInterval);
            resolve();
          } else if (!this.connectionAttemptInProgress) {
            // If attempt finished but didn't succeed
            clearInterval(waitInterval);
            reject(new Error('Connection attempt failed.'));
          }
        }, 500); // Check every 500ms
        return;
      }

      logger.info('Attempting to connect to MongoDB...');
      this.connectionAttemptInProgress = true;
      this.connectionReady = false; // Explicitly set to false at start of attempt

      try {
        await MongoConnection.getInstance().connect();
        this.mongoEnabled = true;
        this.connectionReady = true; // Set ready flag
        logger.info('Connected to MongoDB successfully');

        this.startAutoSave(); // Start auto-save only after successful connection

        // Process any pending operations
        this.processPendingOperations();

        // Reconnect any pending guild settings that might be in memory
        this.syncPendingGuildSettings();

        this.connectionAttemptInProgress = false;
        resolve(); // Resolve the promise on success
      } catch (error) {
        this.mongoEnabled = false;
        this.connectionReady = false; // Ensure flag is false on error
        logger.error('Failed to connect to MongoDB:', error);
        this.connectionAttemptInProgress = false;

        // Reject the promise on initial failure
        reject(error);

        // Schedule a background reconnection attempt (doesn't affect the returned promise)
        logger.info('Scheduling background reconnection attempt in 5 seconds...');
        setTimeout(() => {
          if (!this.isReady) {
            // Only retry if still not connected
            this.connectToMongoDB().catch(() => {
              // Catch potential rejection from the background retry attempt
              logger.warn('Background reconnection attempt also failed.');
            });
          }
        }, 5000);
      }
      // Removed finally block setting connectionAttemptInProgress to false, handled in try/catch
    });
  }

  /**
   * Sync any pending guild settings stored in memory during MongoDB downtime
   */
  private async syncPendingGuildSettings(): Promise<void> {
    try {
      // Safely import client using a try-catch to avoid errors related to sharding
      let client = null;
      try {
        // Only attempt to load the client if we're fully connected
        if (this.isReady) {
          const indexModule = require('../index');
          client = indexModule && indexModule.client;
        }
      } catch {
        logger.warn('Could not access client from index.ts, skipping guild settings sync');
        return;
      }

      // Only proceed if we found a valid client with pendingGuildSettings
      if (client && client.pendingGuildSettings && client.pendingGuildSettings.size > 0) {
        logger.info(
          `Syncing ${client.pendingGuildSettings.size} pending guild settings to MongoDB`
        );

        // Process each pending guild setting
        for (const [guildId, settings] of client.pendingGuildSettings.entries()) {
          try {
            await this.forceSyncGuildSettings(guildId, settings.joinChannelId, settings.categoryId);
            logger.info(`Successfully synced pending settings for guild ${guildId}`);

            // Remove from pending after successful sync
            client.pendingGuildSettings.delete(guildId);
          } catch (syncError) {
            logger.error(`Failed to sync pending settings for guild ${guildId}:`, syncError);
            // Mark with updated timestamp but keep in queue for next attempt
            settings.pendingSince = new Date();
            settings.lastSyncAttempt = new Date();
            settings.syncError = syncError.message;
          }
        }
      }
    } catch (error) {
      logger.error('Error syncing pending guild settings:', error);
    }
  }

  /**
   * Process any operations that were queued while disconnected
   */
  private async processPendingOperations(): Promise<void> {
    if (!this.shouldUseMongoDB()) {
      return;
    }

    for (const [type, operations] of this.pendingOperations.entries()) {
      logger.info(`Processing ${operations.length} pending operations for ${type}`);

      for (const operation of operations) {
        try {
          await operation.func(...operation.args);
        } catch (error) {
          logger.error(`Error processing pending operation for ${type}:`, error);
        }
      }

      this.pendingOperations.delete(type);
    }
  }

  /**
   * Queue an operation for later execution when MongoDB becomes available
   */
  private queueOperation(type: string, func: Function, ...args: any[]): void {
    if (!this.pendingOperations.has(type)) {
      this.pendingOperations.set(type, []);
    }

    this.pendingOperations.get(type)?.push({
      func,
      args,
    });

    logger.debug(`Queued ${type} operation for later execution`);
  }

  /**
   * Attempt an operation with retry logic
   */
  private async withRetry<T>(operation: () => Promise<T>, retries = this.maxRetries): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      if (retries <= 0) {
        throw error;
      }

      logger.warn(`Operation failed, retrying... (${retries} attempts left):`, error);
      await new Promise(resolve => setTimeout(resolve, this.retryDelay));
      return this.withRetry(operation, retries - 1);
    }
  }

  /**
   * Get or set cache data
   */
  getCached<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    const now = Date.now();
    if (now - cached.timestamp > this.cacheTTL) {
      this.cache.delete(key);
      return null;
    }

    return cached.data as T;
  }

  /**
   * Set cache data
   */
  setCache<T>(key: string, data: T): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });
  }

  /**
   * Clear specific cache entry or all cache
   */
  clearCache(key?: string): void {
    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
  }

  /**
   * Check if MongoDB is connected and should be used
   * Uses the new isReady getter.
   */
  shouldUseMongoDB(): boolean {
    // Simply use the ready flag now
    return this.isReady;
    // const mongoConnection = MongoConnection.getInstance();
    // return this.mongoEnabled && mongoConnection.isConnectedToMongoDB(); // Old way
  }

  /**
   * Save data generically
   */
  async saveData(key: string, data: any): Promise<boolean> {
    try {
      if (!this.shouldUseMongoDB()) {
        this.queueOperation('saveData', this.saveData.bind(this), key, data);
        return false;
      }

      // Store in the general collection
      const generalCollection = mongoose.connection.collection('general_data');
      await generalCollection.updateOne(
        { key },
        { $set: { key, data, updatedAt: new Date() } },
        { upsert: true }
      );

      // Update cache
      this.setCache(key, data);
      return true;
    } catch (error) {
      logger.error(`Error saving data for key ${key}:`, error);
      return false;
    }
  }

  /**
   * Load data generically
   */
  async loadData(key: string): Promise<any | null> {
    // Try cache first
    const cached = this.getCached(key);
    if (cached) return cached;

    try {
      if (!this.shouldUseMongoDB()) {
        return null;
      }

      const generalCollection = mongoose.connection.collection('general_data');
      const result = await generalCollection.findOne({ key });

      if (result) {
        this.setCache(key, result.data);
        return result.data;
      }

      return null;
    } catch (error) {
      logger.error(`Error loading data for key ${key}:`, error);
      return null;
    }
  }

  /**
   * Start auto-save interval
   */
  startAutoSave() {
    this.autoSaveTimer = setInterval(() => {
      this.saveAllData();
    }, this.autoSaveInterval);

    logger.debug(`Auto-save started with interval of ${this.autoSaveInterval / 1000} seconds`);
  }

  /**
   * Stop auto-save interval
   */
  stopAutoSave() {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
      this.autoSaveTimer = null;
      logger.debug('Auto-save stopped');
    }
  }

  /**
   * Start periodic validation of data
   */
  startPeriodicValidation() {
    // Every 5 minutes, validate essential data
    const VALIDATION_INTERVAL = 5 * 60 * 1000;

    this.validationInterval = setInterval(() => {
      this.validateData();
    }, VALIDATION_INTERVAL);

    logger.debug(`Validation interval started (every ${VALIDATION_INTERVAL / 60000} minutes)`);
  }

  /**
   * Stop validation interval
   */
  stopValidation() {
    if (this.validationInterval) {
      clearInterval(this.validationInterval);
      this.validationInterval = null;
      logger.debug('Validation interval stopped');
    }
  }

  /**
   * Save all MongoDB data
   */
  async saveAllData() {
    if (!this.shouldUseMongoDB()) {
      logger.error('Cannot save data: MongoDB is not available');
      return false;
    }

    try {
      // MongoDB auto-saves data, but we could add additional logic here if needed
      logger.debug('MongoDB data auto-saved');
      return true;
    } catch (error) {
      logger.error('Error during MongoDB auto-save:', error);
      return false;
    }
  }

  /**
   * Validate data integrity
   */
  async validateData() {
    if (!this.shouldUseMongoDB()) {
      logger.error('Cannot validate data: MongoDB is not available');
      return;
    }

    try {
      // Perform validation checks
      logger.debug('Data validation completed');
    } catch (error) {
      logger.error('Error during data validation:', error);
    }
  }

  /**
   * Load user settings from MongoDB
   * @returns {Promise<Object>} User settings object
   */
  async loadUserSettings() {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
    }

    try {
      const users = await userRepository.find({});
      const result = {};

      // Convert MongoDB documents to format expected by legacy code
      users.forEach(user => {
        result[user.userId] = user.settings;
      });

      return result;
    } catch (error) {
      logger.error('Error loading user settings from MongoDB:', error);
      throw error;
    }
  }

  /**
   * Get settings for a single user from MongoDB
   * @param {string} userId - The user ID
   * @returns {Promise<Object|null>} User settings or null if not found
   */
  async getUserSettings(userId: string): Promise<any> {
    if (!this.shouldUseMongoDB()) {
      logger.warn('Cannot get user settings: MongoDB is not available');
      return null;
    }

    try {
      const user = await userRepository.findByUserId(userId);
      return user ? user.settings : null;
    } catch (error) {
      logger.error(`Error getting user settings for ${userId} from MongoDB:`, error);
      return null;
    }
  }

  /**
   * Save user settings to MongoDB
   * @param {Object} settings - User settings to save
   * @returns {Promise<boolean>} Success status
   */
  async saveUserSettings(settings) {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
    }

    try {
      // Save each user's settings to MongoDB
      for (const [userId, userSettings] of Object.entries(settings)) {
        await userRepository.upsert(
          { userId },
          {
            userId,
            settings: userSettings,
          }
        );
      }
      return true;
    } catch (error) {
      logger.error('Error saving user settings to MongoDB:', error);
      throw error;
    }
  }

  /**
   * Load channel settings from MongoDB
   * @returns {Promise<Object>} Channel settings object
   */
  async loadChannelSettings() {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
    }

    try {
      const channels = await channelRepository.find({});
      const result = {};

      // Convert MongoDB documents to format expected by legacy code
      channels.forEach(channel => {
        result[channel.channelId] = { ...channel.settings, guildId: channel.guildId };
      });

      return result;
    } catch (error) {
      logger.error('Error loading channel settings from MongoDB:', error);
      throw error;
    }
  }

  /**
   * Save channel settings to MongoDB
   * @param {Object} settings - Channel settings to save
   * @returns {Promise<boolean>} Success status
   */
  async saveChannelSettings(settings) {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
    }

    try {
      // Save each channel's settings to MongoDB
      for (const [channelId, channelData] of Object.entries(settings)) {
        const data = channelData as any;
        const guildId = data.guildId || 'unknown';

        await channelRepository.upsert(
          { channelId },
          {
            channelId,
            guildId,
            settings: channelData,
          }
        );
      }
      return true;
    } catch (error) {
      logger.error('Error saving channel settings to MongoDB:', error);
      throw error;
    }
  }

  /**
   * Load guild settings from MongoDB
   * @returns {Promise<Object>} Guild settings object
   */
  async loadGuildSettings() {
    // Try cache first
    const cached = this.getCached('guild_settings');
    if (cached) return cached;

    if (!this.shouldUseMongoDB()) {
      logger.warn('Cannot load guild settings: MongoDB is not available');
      this.queueOperation('loadGuildSettings', this.loadGuildSettings.bind(this));
      return {};
    }

    try {
      return await this.withRetry(async () => {
        const guilds = await guildRepository.find({});
        const result = {};

        // Convert MongoDB documents to format expected by legacy code
        guilds.forEach(guild => {
          result[guild.guildId] = {
            prefix: guild.prefix,
            tempChannelLimit: guild.tempChannelLimit,
            userChannelLimit: guild.userChannelLimit,
            defaultUserLimit: guild.defaultUserLimit,
            autoDeleteEmpty: guild.autoDeleteEmpty,
            autoDeleteDelay: guild.autoDeleteDelay,
            joinChannelId: guild.joinChannelId,
            tempCategoryId: guild.tempCategoryId,
            lastUpdated: guild.lastUpdated,
          };
        });

        // Cache the result for future use
        this.setCache('guild_settings', result);
        return result;
      });
    } catch (error) {
      logger.error('Error loading guild settings from MongoDB:', error);
      throw error;
    }
  }

  /**
   * Save guild settings to MongoDB
   * @param {Object} settings - Guild settings to save
   * @returns {Promise<boolean>} Success status
   */
  async saveGuildSettings(settings) {
    if (!this.shouldUseMongoDB()) {
      logger.warn('Cannot save guild settings: MongoDB is not available');
      this.queueOperation('saveGuildSettings', this.saveGuildSettings.bind(this), settings);
      return false;
    }

    try {
      return await this.withRetry(async () => {
        // Save each guild's settings to MongoDB
        for (const [guildId, guildSettings] of Object.entries(settings)) {
          await guildRepository.upsert(
            { guildId },
            {
              guildId,
              ...(guildSettings as any),
              lastUpdated: new Date(),
            }
          );
        }

        // Update cache after successful save
        this.setCache('guild_settings', settings);
        this.clearCache('guild_' + Object.keys(settings).join('_'));

        logger.info(`Successfully saved settings for ${Object.keys(settings).length} guilds`);
        return true;
      });
    } catch (error) {
      logger.error('Error saving guild settings to MongoDB:', error);
      return false;
    }
  }

  /**
   * Get per-guild settings, with fallback to defaults
   * @param {string} guildId - ID of the guild
   * @param {boolean} useCache - Whether to use cache (default: false)
   * @returns {Promise<Object>} Guild settings with defaults applied
   */
  async getGuildSettings(guildId: string, useCache: boolean = false): Promise<any> {
    // Only try cache if explicitly requested
    if (useCache) {
      const cacheKey = `guild_${guildId}`;
      const cached = this.getCached(cacheKey);
      if (cached) {
        logger.debug(`Using cached settings for guild ${guildId}`);
        return cached;
      }
    }

    if (!this.shouldUseMongoDB()) {
      logger.warn(`Cannot get guild settings: MongoDB is not available for guild ${guildId}`);
      const defaultSettings = { guildId, ...DEFAULT_GUILD_SETTINGS, lastUpdated: new Date() };

      // Queue the operation for when MongoDB becomes available
      this.queueOperation('getGuildSettings', this.getGuildSettings.bind(this), guildId);

      return defaultSettings;
    }

    try {
      return await this.withRetry(async () => {
        logger.info(`Fetching settings from MongoDB for guild ${guildId}`);
        const settings = await guildRepository.findByGuildId(guildId, false);

        if (settings) {
          // Log the exact MongoDB settings being returned
          logger.info(
            `Found settings in MongoDB for guild ${guildId}: JoinChannel=${settings.joinChannelId}, TempCategory=${settings.tempCategoryId}`
          );

          // Convert Mongoose document to plain object and ensure all required fields
          const plainSettings = settings.toObject ? settings.toObject() : settings;
          const result = {
            ...DEFAULT_GUILD_SETTINGS,
            ...plainSettings,
            guildId,
          };

          // Only cache the result if caching is requested
          // if (useCache) {
          const cacheKey = `guild_${guildId}`;
          this.setCache(cacheKey, result);
          // }

          return result;
        } else {
          logger.warn(
            `No settings found for guild ${guildId} in MongoDB. Creating default settings.`
          );

          // Create default settings in MongoDB
          const defaultSettings = { guildId, ...DEFAULT_GUILD_SETTINGS, lastUpdated: new Date() };
          await guildRepository.create(defaultSettings);

          // Cache the default settings only if caching is requested
          if (useCache) {
            const cacheKey = `guild_${guildId}`;
            this.setCache(cacheKey, defaultSettings);
          }

          return defaultSettings;
        }
      });
    } catch (error) {
      logger.error(`Error retrieving guild settings for ${guildId} from MongoDB:`, error);

      // Return default settings in case of error
      const defaultSettings = { guildId, ...DEFAULT_GUILD_SETTINGS, lastUpdated: new Date() };
      return defaultSettings;
    }
  }

  /**
   * Update guild settings
   * @param {string} guildId - ID of the guild
   * @param {Object} settingsToUpdate - Settings to update
   * @returns {Promise<Object>} Updated guild settings
   */
  async updateGuildSettings(guildId: string, settingsToUpdate: any): Promise<any> {
    const cacheKey = `guild_${guildId}`;

    if (!this.shouldUseMongoDB()) {
      logger.warn(`Cannot update guild settings: MongoDB is not available for guild ${guildId}`);
      this.queueOperation(
        'updateGuildSettings',
        this.updateGuildSettings.bind(this),
        guildId,
        settingsToUpdate
      );

      // Apply updates to cached settings if they exist
      const cached = this.getCached(cacheKey);
      if (cached) {
        // Create new object with default settings and apply updates
        const updated = {
          ...DEFAULT_GUILD_SETTINGS,
          guildId,
          ...settingsToUpdate,
          lastUpdated: new Date(),
        };

        this.setCache(cacheKey, updated);
        // Emit settings changed event
        this.emit('guildSettingsChanged', guildId, updated);
        return updated;
      }

      // Create result with defaults and updates
      const result = {
        ...DEFAULT_GUILD_SETTINGS,
        guildId,
        ...settingsToUpdate,
        lastUpdated: new Date(),
      };

      // Emit settings changed event
      this.emit('guildSettingsChanged', guildId, result);
      return result;
    }

    try {
      // Prepare update object with defaults and new settings
      // We're not merging with existing settings to avoid keeping stale values
      const updateData = {
        ...DEFAULT_GUILD_SETTINGS,
        guildId,
        ...settingsToUpdate,
        lastUpdated: new Date(),
      };

      // Update or create the document, fully replacing all fields
      // Using replaceOne instead of update to ensure complete replacement
      await GuildSettingsModel.replaceOne({ guildId }, updateData, { upsert: true });

      logger.info(`Successfully updated guild settings for guild ${guildId} in MongoDB`);

      // Clear all caches related to this guild
      this.clearCache(cacheKey);
      this.clearCache('guild_settings');

      // Re-read settings to ensure consistency
      const newSettings = await guildRepository.findByGuildId(guildId);

      // Prepare result
      let result;

      if (newSettings) {
        const plainObj = newSettings.toObject ? newSettings.toObject() : newSettings;
        result = {
          ...DEFAULT_GUILD_SETTINGS,
          ...plainObj,
        };
      } else {
        result = {
          ...DEFAULT_GUILD_SETTINGS,
          guildId,
          ...settingsToUpdate,
          lastUpdated: new Date(),
        };
      }

      // Update cache with fresh data
      this.setCache(cacheKey, result);

      // Emit settings changed event
      this.emit('guildSettingsChanged', guildId, result);

      return result;
    } catch (error) {
      logger.error(`Error updating guild settings for ${guildId} in MongoDB:`, error);

      // Create result with defaults and updates
      const result = {
        ...DEFAULT_GUILD_SETTINGS,
        guildId,
        ...settingsToUpdate,
        lastUpdated: new Date(),
      };

      return result;
    }
  }

  /**
   * Load blacklisted servers from MongoDB
   * @returns {Promise<Object>} Blacklisted servers object
   */
  async loadBlacklistedServers() {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
    }

    try {
      const blacklistedObj = { blacklistedServers: {} };
      const blacklisted = await blacklistedServerRepository.find({});

      // Convert MongoDB documents to format expected by legacy code
      blacklisted.forEach(server => {
        blacklistedObj.blacklistedServers[server.guildId] = {
          reason: server.reason,
          blacklistedBy: server.blacklistedBy,
          blacklistedAt: server.blacklistedAt,
        };
      });

      return blacklistedObj;
    } catch (error) {
      logger.error('Error loading blacklisted servers from MongoDB:', error);
      throw error;
    }
  }

  /**
   * Save blacklisted servers to MongoDB
   * @param {Object} data - Blacklisted servers data to save
   * @returns {Promise<boolean>} Success status
   */
  async saveBlacklistedServers(data) {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
    }

    try {
      if (data && data.blacklistedServers) {
        for (const [guildId, blacklistData] of Object.entries(data.blacklistedServers)) {
          const typedData = blacklistData as any;
          await blacklistedServerRepository.upsert(
            { guildId },
            {
              guildId,
              reason: typedData.reason || 'No reason provided',
              blacklistedBy: typedData.blacklistedBy || 'unknown',
              blacklistedAt: typedData.blacklistedAt
                ? new Date(typedData.blacklistedAt)
                : new Date(),
            }
          );
        }
      }
      return true;
    } catch (error) {
      logger.error('Error saving blacklisted servers to MongoDB:', error);
      throw error;
    }
  }

  /**
   * Load temporary channels from MongoDB
   * @returns {Promise<Object>} Temporary channels object mapping channel IDs to owner user IDs
   */
  async loadTempChannels() {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
    }

    try {
      return await tempChannelRepository.getAllTempChannels();
    } catch (error) {
      logger.error('Error loading temp channels from MongoDB:', error);
      throw error;
    }
  }

  /**
   * Save temporary channels to MongoDB
   * @param {Map|Object} channels - Map or object of channel IDs to owner user IDs
   * @returns {Promise<boolean>} Success status
   */
  async saveTempChannels(channels) {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
    }

    let channelsObj = channels;

    // Convert Map to Object if needed
    if (channels instanceof Map) {
      channelsObj = {};
      for (const [channelId, userId] of channels.entries()) {
        channelsObj[channelId] = userId;
      }
    }

    try {
      // Get current channels from database to identify which ones to remove
      const currentChannelsInDB = await tempChannelRepository.getAllTempChannels();
      const currentChannelIds = Object.keys(currentChannelsInDB);
      const newChannelIds = Object.keys(channelsObj);

      // Find channels that exist in DB but not in current state (need to be removed)
      const channelsToRemove = currentChannelIds.filter(id => !newChannelIds.includes(id));

      // Remove stale channels from database
      for (const channelId of channelsToRemove) {
        try {
          await tempChannelRepository.removeTempChannel(channelId);
          logger.debug(`Removed stale channel ${channelId} from database`);
        } catch (error) {
          logger.warn(`Failed to remove stale channel ${channelId} from database:`, error);
        }
      }

      // Add/update current channels
      for (const [channelId, ownerId] of Object.entries(channelsObj)) {
        // Get the channel from Discord to determine its guild ID
        try {
          // Try to access Discord client to get guild information
          const client = require('../index').client;
          const channel = client?.channels?.cache?.get(channelId);
          const guildId = channel?.guild?.id || 'unknown';

          await tempChannelRepository.upsert(
            { channelId },
            {
              channelId,
              ownerId: ownerId as string,
              guildId: guildId,
              createdAt: new Date(),
            }
          );
        } catch {
          // If we can't get the guild ID, still save with unknown
          logger.warn(`Could not determine guild ID for channel ${channelId}, using 'unknown'`);
          await tempChannelRepository.upsert(
            { channelId },
            {
              channelId,
              ownerId: ownerId as string,
              guildId: 'unknown',
              createdAt: new Date(),
            }
          );
        }
      }

      // Log the results
      if (channelsToRemove.length > 0) {
        logger.info(`Removed ${channelsToRemove.length} stale channels from database`);
      }
      logger.info(`Saved ${Object.keys(channelsObj).length} temporary channels to MongoDB`);
      return true;
    } catch (error) {
      logger.error('Error saving temp channels to MongoDB:', error);
      throw error;
    }
  }

  /**
   * Clean up stale temporary channels from database
   * Removes channels that no longer exist in Discord
   * @returns {Promise<number>} Number of channels removed
   */
  async cleanupStaleTempChannels() {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
      return 0;
    }

    try {
      const client = require('../index').client;
      if (!client || !client.guilds) {
        logger.warn('Discord client not available for stale channel cleanup');
        return 0;
      }

      // Get all channels from database
      const allChannels = await tempChannelRepository.getAllTempChannels();
      const channelIds = Object.keys(allChannels);

      if (channelIds.length === 0) {
        logger.debug('No temporary channels in database to check');
        return 0;
      }

      let removedCount = 0;

      // Check each channel to see if it still exists in Discord
      for (const channelId of channelIds) {
        let channelExists = false;

        // Search for the channel in all guilds
        for (const guild of client.guilds.cache.values()) {
          try {
            const channel = guild.channels.cache.get(channelId);
            if (channel) {
              channelExists = true;
              break;
            }
          } catch (error) {
            // Continue checking other guilds
            continue;
          }
        }

        // If channel doesn't exist in any guild, remove it from database
        if (!channelExists) {
          try {
            await tempChannelRepository.removeTempChannel(channelId);
            removedCount++;
            logger.debug(`Removed stale channel ${channelId} from database (doesn't exist in Discord)`);
          } catch (error) {
            logger.warn(`Failed to remove stale channel ${channelId} from database:`, error);
          }
        }
      }

      if (removedCount > 0) {
        logger.info(`Database cleanup: Removed ${removedCount} stale channels from database`);
      } else {
        logger.debug('Database cleanup: No stale channels found');
      }

      return removedCount;
    } catch (error) {
      logger.error('Error during stale channel cleanup:', error);
      return 0;
    }
  }

  /**
   * Clean up invalid guild settings (channels that no longer exist)
   * @returns {Promise<number>} Number of guilds cleaned up
   */
  async cleanupInvalidGuildSettings() {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
      return 0;
    }

    try {
      const client = require('../index').client;
      if (!client || !client.guilds) {
        logger.warn('Discord client not available for guild settings cleanup');
        return 0;
      }

      // Get all guild settings
      const allGuildSettings = await this.loadGuildSettings();
      let cleanedCount = 0;

      for (const [guildId, settings] of Object.entries(allGuildSettings)) {
        const guild = client.guilds.cache.get(guildId);
        if (!guild) {
          // Guild no longer exists, but we'll keep the settings in case the bot rejoins
          continue;
        }

        let needsUpdate = false;
        const updates: any = {};

        // Check if join channel still exists
        if (settings.joinChannelId) {
          try {
            const joinChannel = await guild.channels.fetch(settings.joinChannelId).catch(() => null);
            if (!joinChannel) {
              logger.warn(`Join channel ${settings.joinChannelId} no longer exists in guild ${guildId}, removing from settings`);
              updates.joinChannelId = null;
              needsUpdate = true;
            }
          } catch (error) {
            if (error.message === 'Unknown Channel') {
              logger.warn(`Join channel ${settings.joinChannelId} no longer exists in guild ${guildId}, removing from settings`);
              updates.joinChannelId = null;
              needsUpdate = true;
            }
          }
        }

        // Check if temp category still exists
        if (settings.tempCategoryId) {
          try {
            const category = await guild.channels.fetch(settings.tempCategoryId).catch(() => null);
            if (!category) {
              logger.warn(`Temp category ${settings.tempCategoryId} no longer exists in guild ${guildId}, removing from settings`);
              updates.tempCategoryId = null;
              needsUpdate = true;
            }
          } catch (error) {
            if (error.message === 'Unknown Channel') {
              logger.warn(`Temp category ${settings.tempCategoryId} no longer exists in guild ${guildId}, removing from settings`);
              updates.tempCategoryId = null;
              needsUpdate = true;
            }
          }
        }

        // Update settings if needed
        if (needsUpdate) {
          try {
            await this.updateGuildSettings(guildId, updates);
            cleanedCount++;
            logger.info(`Cleaned up invalid settings for guild ${guildId}`);
          } catch (error) {
            logger.error(`Failed to clean up settings for guild ${guildId}:`, error);
          }
        }
      }

      if (cleanedCount > 0) {
        logger.info(`Guild settings cleanup: Fixed ${cleanedCount} guilds with invalid channel references`);
      } else {
        logger.debug('Guild settings cleanup: No invalid settings found');
      }

      return cleanedCount;
    } catch (error) {
      logger.error('Error during guild settings cleanup:', error);
      return 0;
    }
  }

  /**
   * Check if a server is blacklisted
   * @param {string} guildId - ID of the guild to check
   * @returns {Promise<Object|null>} Blacklist data or null if not blacklisted
   */
  async isServerBlacklisted(guildId) {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
    }

    try {
      const blacklistedServer = await blacklistedServerRepository.findByGuildId(guildId);
      if (blacklistedServer) {
        return {
          reason: blacklistedServer.reason,
          blacklistedBy: blacklistedServer.blacklistedBy,
          blacklistedAt: blacklistedServer.blacklistedAt,
        };
      }
      return null;
    } catch (error) {
      logger.error(`Error checking if server ${guildId} is blacklisted in MongoDB:`, error);
      throw error;
    }
  }

  /**
   * Get a guild's prefix
   * @param {string} guildId - ID of the guild
   * @returns {Promise<string>} Guild prefix or default prefix
   */
  async getGuildPrefix(guildId) {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
    }

    try {
      return await guildRepository.getGuildPrefix(guildId);
    } catch (error) {
      logger.error(`Error getting guild prefix for ${guildId} from MongoDB:`, error);

      // Return default prefix from constants
      const { DEFAULT_PREFIX } = require('../constants/devs');
      return DEFAULT_PREFIX;
    }
  }

  /**
   * Set a guild's prefix
   * @param {string} guildId - ID of the guild
   * @param {string} prefix - New prefix
   * @returns {Promise<boolean>} Success status
   */
  async setGuildPrefix(guildId, prefix) {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
    }

    try {
      await guildRepository.setGuildPrefix(guildId, prefix);

      // Clear all guild-related caches to ensure updates are immediately reflected
      this.clearCache(`guild_${guildId}`);
      this.clearCache('guild_settings');

      // Emit prefix changed event
      this.emit('guildPrefixChanged', guildId, prefix);

      return true;
    } catch (error) {
      logger.error(`Error setting guild prefix for ${guildId} in MongoDB:`, error);
      throw error;
    }
  }

  /**
   * Save bot performance statistics to MongoDB
   * @param {Object} stats - Performance statistics to save
   */
  async saveBotStats(stats: any): Promise<void> {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
    }

    try {
      // Save stats to a dedicated collection
      const statsCollection = mongoose.connection.collection('bot_stats');
      await statsCollection.insertOne(stats);
      logger.info('Successfully saved bot stats to MongoDB');
    } catch (error) {
      logger.error('Failed to save bot stats to MongoDB:', error);
      throw error;
    }
  }

  /**
   * Cleanup resources before shutdown
   */
  async cleanup() {
    try {
      // First stop all intervals to prevent any new operations
      this.stopAutoSave();
      this.stopValidation();

      // Log cached data sizes
      logger.info(
        `Cleanup: Flushing ${this.cache.size} cached items and ${Array.from(this.pendingOperations.keys()).length} pending operations`
      );

      // Flush any pending operations
      if (this.shouldUseMongoDB()) {
        await this.processPendingOperations();
      }

      // Then save any pending data to MongoDB
      const saveResult = await this.saveAllData();
      logger.info(`Final data save ${saveResult ? 'succeeded' : 'failed'}`);

      // Finally, close the MongoDB connection
      try {
        await MongoConnection.getInstance().disconnect();
        logger.info('MongoDB connection closed successfully');
      } catch (dbError) {
        logger.error('Error closing MongoDB connection:', dbError);
      }

      // Clear all caches
      this.clearCache();
      logger.info('All caches cleared');

      logger.info('Data manager cleanup completed');
      return true;
    } catch (error) {
      logger.error('Error during data manager cleanup:', error);
      return false;
    }
  }

  /**
   * Force synchronize guild settings with the database
   * Used as a fallback when normal update methods fail
   * @param guildId - The guild ID
   * @param joinChannelId - The join channel ID
   * @param categoryId - The category ID
   */
  async forceSyncGuildSettings(
    guildId: string,
    joinChannelId: string,
    categoryId: string
  ): Promise<boolean> {
    try {
      logger.info(
        `Force syncing guild settings for ${guildId} with Join=${joinChannelId}, Category=${categoryId}`
      );

      // Clear all caches immediately
      this.clearCache(`guild_${guildId}`);
      this.clearCache('guild_settings');

      // Create a complete settings object
      const settings = {
        ...DEFAULT_GUILD_SETTINGS,
        guildId,
        joinChannelId,
        tempCategoryId: categoryId,
        lastUpdated: new Date(),
      };

      // Direct database update using the model
      await GuildSettingsModel.deleteOne({ guildId });
      await GuildSettingsModel.create(settings);

      // Verify update by direct query to MongoDB
      const collection = mongoose.connection.collection('guildsettings');
      const result = await collection.findOne({ guildId });

      if (result) {
        logger.info(
          `Force sync successful - verified in database: ${JSON.stringify({
            joinChannelId: result.joinChannelId,
            tempCategoryId: result.tempCategoryId,
          })}`
        );

        // Update the cache with the new settings
        const cacheKey = `guild_${guildId}`;
        this.setCache(cacheKey, settings);

        // Also update the global settings cache
        const allSettings = await this.loadGuildSettings();
        if (allSettings && typeof allSettings === 'object') {
          allSettings[guildId] = settings;
          this.setCache('guild_settings', allSettings);
        }

        // Emit event for listeners
        this.emit('guildSettingsChanged', guildId, settings);

        return true;
      } else {
        logger.error(`Force sync failed - could not find document after update`);
        return false;
      }
    } catch (error) {
      logger.error(`Force sync failed with error: ${error.message}`);
      return false;
    }
  }

  /**
   * Load all guild prefixes into memory
   * Call this during bot startup to reduce database calls
   */
  async preloadGuildPrefixes() {
    if (!this.shouldUseMongoDB()) {
      logger.warn('Cannot preload guild prefixes: MongoDB is not available');
      return;
    }

    try {
      logger.info('Preloading guild prefixes...');
      const guilds = await guildRepository.find({});
      const { DEFAULT_PREFIX } = require('../constants/devs');

      let count = 0;
      for (const guild of guilds) {
        // Use the same logic as getGuildPrefix
        const prefix = guild.prefix || DEFAULT_PREFIX || DEFAULT_GUILD_SETTINGS.prefix;

        // Emit event to update all listeners
        this.emit('guildPrefixChanged', guild.guildId, prefix);
        count++;
      }

      logger.info(`Successfully preloaded ${count} guild prefixes`);
    } catch (error) {
      logger.error('Error preloading guild prefixes:', error);
    }
  }
}

// Export singleton instance
const dataManagerInstance = new DataManager();
export default dataManagerInstance;
