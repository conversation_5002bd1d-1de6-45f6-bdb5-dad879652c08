/**
 * Guild Delete Event
 *
 * Triggered when the bot is removed from a server (kicked, banned, or left).
 * Logs detailed information about the guild and the event.
 */
import { Events } from 'discord.js';
import logger from '../utils/logger';
import guildLogger from '../utils/guildLogger';
import dataManager from '../utils/dataManager';

export const name = Events.GuildDelete;
export const once = false;
export const execute = async (guild, client) => {
  try {
    // Determine if the bot was kicked/banned or if it left voluntarily
    // Unfortunately, Discord.js doesn't provide a direct way to determine this
    // We'll check if the guild is available to make an educated guess

    let leaveReason = 'unknown';

    // If the guild is unavailable, it might be a Discord outage rather than a kick
    if (guild.available === false) {
      leaveReason = 'guild_unavailable';
      logger.warn(`Guild ${guild.id} became unavailable, might be a Discord outage`);
      return; // Don't log this as a removal event
    }

    // Check if this was a manual leave from our blacklist system
    const blacklistInfo = await dataManager.isServerBlacklisted(guild.id);
    if (blacklistInfo) {
      leaveReason = `blacklisted:${blacklistInfo.reason}`;
    } else {
      // If not blacklisted, assume it was a kick/ban
      leaveReason = 'kicked_or_banned';
    }

    // Log the event with detailed information
    guildLogger.logGuildLeave(guild, leaveReason);

    // Clean up any guild-specific data
    try {
      // Remove temporary channels for this guild
      const tempChannelsToRemove = [];

      // Find all temp channels for this guild
      for (const [channelId, userId] of client.tempChannels.entries()) {
        const channel = client.channels.cache.get(channelId);
        if (channel && channel.guild && channel.guild.id === guild.id) {
          tempChannelsToRemove.push(channelId);
        }
      }

      // Remove the channels from tracking
      tempChannelsToRemove.forEach(channelId => {
        client.tempChannels.delete(channelId);
      });

      // Save the updated tempChannels Map
      if (tempChannelsToRemove.length > 0) {
        dataManager.saveTempChannels(client.tempChannels);
        logger.info(
          `Removed ${tempChannelsToRemove.length} temporary channels for guild ${guild.id}`
        );
      }

      // Note: We're intentionally not removing guild settings in case the bot is re-added
      // This preserves user configurations if the bot is temporarily removed
    } catch (cleanupError) {
      logger.error(`Error cleaning up data for guild ${guild.id}:`, cleanupError);
    }
  } catch (error) {
    logger.error(`Error handling guild leave for ${guild.id}:`, error);
  }
};
