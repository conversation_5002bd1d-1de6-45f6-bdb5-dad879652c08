/**
 * Logger utility for MyVC Bot
 * Provides consistent logging format with timestamps and log levels
 * Supports sending error logs to Discord webhooks
 */
import fs from 'fs';
import path from 'path';
import { formatBytes } from './helpers';
// Import webhook utility (using dynamic import to avoid circular dependencies)
let webhookUtil: any = null;

// We'll load the webhook utility dynamically to avoid circular dependencies
const loadWebhookUtil = async () => {
  if (!webhookUtil) {
    try {
      // Dynamic import to avoid circular dependency
      const module = await import('./webhook');
      webhookUtil = module.default;
    } catch (error) {
      console.error('Failed to load webhook utility:', error);
    }
  }
  return webhookUtil;
};

// Log levels with improved visual styling
const LOG_LEVELS: {
  [key: string]: { priority: number; color: string; emoji: string; textColor: string };
} = {
  ERROR: { priority: 0, color: '\x1b[41m\x1b[37m', emoji: '❌', textColor: '\x1b[31m' }, // White on Red background + Red text
  WARN: { priority: 1, color: '\x1b[43m\x1b[30m', emoji: '⚠️', textColor: '\x1b[33m' }, // Black on Yellow background + Yellow text
  INFO: { priority: 2, color: '\x1b[42m\x1b[30m', emoji: 'ℹ️', textColor: '\x1b[32m' }, // Black on Green background + Green text
  DEBUG: { priority: 3, color: '\x1b[46m\x1b[30m', emoji: '🔍', textColor: '\x1b[36m' }, // Black on Cyan background + Cyan text
};

// Reset color code
const RESET_COLOR = '\x1b[0m';
// Bold text
const BOLD = '\x1b[1m';
// Underline
const UNDERLINE = '\x1b[4m';
// Dim text
const DIM = '\x1b[2m';

// Default console log level from environment or INFO
const DEFAULT_CONSOLE_LOG_LEVEL = (process.env.CONSOLE_LOG_LEVEL || 'INFO').toUpperCase();
// File log level is always ERROR
const FILE_LOG_LEVEL = 'ERROR';

// Automatically detect and use the system timezone without environment variable dependency
const TIMEZONE = Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC';

// Log directory
const LOG_DIR = path.join(process.cwd(), 'logs');

// Ensure log directory exists
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

/**
 * Format a date to a readable string using the local system time
 * @param {Date} date - The date to format
 * @returns {string} The formatted date string
 */
function formatLocalDate(date: Date): string {
  try {
    // Get local date parts
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');

    // Format as YYYY-MM-DD HH:MM:SS
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (error) {
    console.error(`Error formatting date:`, error);
    return date.toISOString(); // Fallback to ISO format
  }
}

/**
 * Format object for better console display
 * @param {any} obj - The object to format
 * @returns {string} Formatted string representation of the object
 */
function formatObject(obj: any): string {
  if (obj === null) return 'null';
  if (obj === undefined) return 'undefined';

  try {
    if (typeof obj === 'object') {
      // For errors, extract the message
      if (obj instanceof Error) {
        return `${obj.name}: ${obj.message}${obj.stack ? `\n${obj.stack}` : ''}`;
      }

      // For small objects, make them compact
      if (Object.keys(obj).length <= 3) {
        return JSON.stringify(obj);
      }

      // For larger objects, use pretty formatting with indentation
      return JSON.stringify(obj, null, 2);
    }
    return String(obj);
  } catch (e) {
    return `[Unserializable Object: ${typeof obj}]`;
  }
}

// Logger class
class Logger {
  consoleLevel: string;
  logToFile: boolean;
  logToConsole: boolean;
  maxLogFiles: number; // Keep this many hourly files
  currentLogFile: string | null = null;
  currentLogHour: number = -1;
  timezone: string;
  useLocalTime: boolean;
  showEmojis: boolean;
  compactMode: boolean;

  constructor(
    options: {
      consoleLevel?: string;
      logToFile?: boolean;
      logToConsole?: boolean;
      maxLogFiles?: number; // How many hourly files to keep
      timezone?: string; // Timezone for log timestamps
      useLocalTime?: boolean; // Whether to use local formatted time instead of ISO
      showEmojis?: boolean; // Whether to show emojis in logs
      compactMode?: boolean; // Use more compact formatting
    } = {}
  ) {
    this.consoleLevel = options.consoleLevel || DEFAULT_CONSOLE_LOG_LEVEL;
    this.logToFile = options.logToFile !== false;
    this.logToConsole = options.logToConsole !== false;
    this.timezone = options.timezone || TIMEZONE;
    this.useLocalTime = options.useLocalTime !== false;
    this.showEmojis = options.showEmojis !== false;
    this.compactMode = options.compactMode === true;
    // Keep e.g., 24 hours of logs by default
    this.maxLogFiles = options.maxLogFiles || 24;

    // Removed startup cleanup call: this.cleanupAllLogs();
    this.updateLogFile(); // Set initial log file
    // Clean up excess files on startup (based on new hourly pattern)
    this.cleanupOldLogFiles();
  }

  /**
   * Updates the current log file path based on the current hour.
   * Creates a new file if the hour has changed.
   */
  updateLogFile() {
    const now = new Date();
    const hour = now.getHours();

    if (hour !== this.currentLogHour) {
      const dateStr = now.toISOString().slice(0, 10); // YYYY-MM-DD
      const hourStr = hour.toString().padStart(2, '0'); // HH
      const newLogFile = path.join(LOG_DIR, `MyVC-${dateStr}-${hourStr}.log`);
      this.currentLogFile = newLogFile;
      this.currentLogHour = hour;
      // Touch the file to ensure it exists immediately
      try {
        fs.closeSync(fs.openSync(this.currentLogFile, 'a'));
      } catch (err) {
        console.error(`Error creating log file ${this.currentLogFile}:`, err);
      }
      // Clean up old files when a new one is created
      this.cleanupOldLogFiles();
    }
  }

  /**
   * Set current console log level
   * @param {string} level - Log level to set
   */
  setLevel(level: string) {
    const upperLevel = level.toUpperCase();
    if (LOG_LEVELS[upperLevel]) {
      this.consoleLevel = upperLevel;
      this.info(`Console log level set to ${upperLevel}`); // Log this change as INFO to console
    } else {
      this.warn(`Invalid log level: ${level}, console level remains ${this.consoleLevel}`);
    }
  }

  /**
   * Set the timezone for log timestamps
   * @param {string} timezone - Timezone to use (e.g., 'Asia/Karachi')
   */
  setTimezone(timezone: string) {
    try {
      // Test if the timezone is valid
      new Date().toLocaleString('en-US', { timeZone: timezone });
      this.timezone = timezone;
      this.info(`Timezone set to ${timezone}`);
    } catch (error) {
      this.error(`Invalid timezone: ${timezone}. Using ${this.timezone} instead.`, error);
    }
  }

  /**
   * Toggle between ISO and local time format
   * @param {boolean} useLocal - Whether to use local time format
   */
  setUseLocalTime(useLocal: boolean) {
    this.useLocalTime = useLocal;
    this.info(`Using ${useLocal ? 'local time' : 'ISO'} format for timestamps`);
  }

  /**
   * Toggle emoji display in logs
   * @param {boolean} show - Whether to show emojis
   */
  setShowEmojis(show: boolean) {
    this.showEmojis = show;
    this.info(`Emojis in logs: ${show ? 'enabled' : 'disabled'}`);
  }

  /**
   * Toggle compact mode
   * @param {boolean} compact - Whether to use compact mode
   */
  setCompactMode(compact: boolean) {
    this.compactMode = compact;
    this.info(`Compact mode: ${compact ? 'enabled' : 'disabled'}`);
  }

  /**
   * Get formatted timestamp for logging
   * @returns {string} Formatted timestamp
   */
  getTimestamp(): string {
    const now = new Date();
    return this.useLocalTime ? formatLocalDate(now) : now.toISOString();
  }

  /**
   * Log a message with the specified level
   * @param {string} level - Log level
   * @param {string} message - Message to log
   * @param {object | string | null} data - Additional data to log
   */
  log(level: string, message: string, data: object | string | null = null) {
    const upperLevel = level.toUpperCase();
    if (!LOG_LEVELS[upperLevel]) {
      console.warn(`Invalid log level used: ${level}`);
      return; // Ignore invalid levels
    }

    const timestamp = this.getTimestamp();
    const levelInfo = LOG_LEVELS[upperLevel];
    const emoji = this.showEmojis ? `${levelInfo.emoji} ` : '';

    // Format for file log (plain, no colors)
    const fileLogPrefix = `[${timestamp}] [${upperLevel}]`;
    let fileLogMessage = `${fileLogPrefix} ${message}`;

    // Format for console log (with colors and styling)
    let levelLabel = this.compactMode
      ? upperLevel.charAt(0) // Just the first letter in compact mode
      : upperLevel.padEnd(5); // Pad to 5 chars in normal mode

    // Apply styling to level label
    levelLabel = `${levelInfo.color} ${levelLabel} ${RESET_COLOR}`;

    // Format timestamp with dim styling
    const styledTimestamp = `${DIM}[${timestamp}]${RESET_COLOR}`;

    // Build console message
    let consoleMessage = `${styledTimestamp} ${levelLabel} ${emoji}${levelInfo.textColor}${message}${RESET_COLOR}`;

    // Add data if provided
    if (data) {
      // For file logs
      const dataString = typeof data === 'object' ? JSON.stringify(data) : String(data);
      fileLogMessage += ` ${dataString}`;

      // For console display, we format differently based on type
      if (data && typeof data === 'object') {
        const formattedData = formatObject(data);
        // Add formatted data to console message on a new line if it's multiline
        if (formattedData.includes('\n')) {
          consoleMessage += '\n' + DIM + formattedData + RESET_COLOR;
        } else {
          consoleMessage += ' ' + DIM + formattedData + RESET_COLOR;
        }
      } else if (data) {
        consoleMessage += ' ' + DIM + data + RESET_COLOR;
      }
    }

    // Log to console based on consoleLevel
    if (
      this.logToConsole &&
      LOG_LEVELS[upperLevel].priority <= LOG_LEVELS[this.consoleLevel].priority
    ) {
      console.log(consoleMessage);
    }

    // Log to file ONLY if it's an ERROR level message
    if (this.logToFile && upperLevel === FILE_LOG_LEVEL) {
      this.updateLogFile(); // Ensure we're writing to the correct hourly file
      this.appendToFile(`${fileLogMessage}\n`);
    }
  }

  /**
   * Error level log
   * Also sends errors to Discord webhook if configured
   */
  async error(message: string, data: object | string | null = null) {
    this.log('ERROR', message, data);

    // Send to webhook if available
    try {
      const webhook = await loadWebhookUtil();
      if (webhook && webhook.sendErrorWebhook) {
        // Don't await this to avoid blocking the logger
        webhook.sendErrorWebhook(message, data).catch(() => {
          // Silently fail if webhook sending fails
        });
      }
    } catch (error) {
      // Don't log this error to avoid potential infinite loops
      console.error('Failed to send error to webhook:', error);
    }
  }

  /**
   * Warning level log
   */
  warn(message: string, data: object | string | null = null) {
    this.log('WARN', message, data);
  }

  /**
   * Info level log
   */
  info(message: string, data: object | string | null = null) {
    this.log('INFO', message, data);
  }

  /**
   * Debug level log
   */
  debug(message: string, data: object | string | null = null) {
    this.log('DEBUG', message, data);
  }

  /**
   * Append message to log file
   */
  appendToFile(message: string) {
    if (!this.currentLogFile) {
      console.error('Log file path is not set, cannot append log.');
      return;
    }
    try {
      fs.appendFileSync(this.currentLogFile, message);
      // No need to check size anymore: this.checkLogFileSize();
    } catch (err) {
      console.error('Error writing to log file:', err);
    }
  }

  /**
   * Clean up old log files to prevent disk space issues
   * Only keeps the most recent files based on maxLogFiles setting (hourly files)
   */
  cleanupOldLogFiles() {
    try {
      const files = fs.readdirSync(LOG_DIR);
      const logFiles = files
        // Match the new hourly format: MyVC-YYYY-MM-DD-HH.log
        .filter(file => /^MyVC-\d{4}-\d{2}-\d{2}-\d{2}\.log$/.test(file))
        .map(file => {
          try {
            return {
              name: file,
              path: path.join(LOG_DIR, file),
              stats: fs.statSync(path.join(LOG_DIR, file)),
            };
          } catch (statErr) {
            // Handle case where file might be deleted between readdir and stat
            console.error(`Error stating file ${file}:`, statErr);
            return null;
          }
        })
        .filter(fileInfo => fileInfo !== null) // Filter out nulls from stat errors
        // Sort by modification time, newest first
        .sort((a, b) => b!.stats.mtime.getTime() - a!.stats.mtime.getTime());

      // Keep only the most recent files based on maxLogFiles setting
      if (logFiles.length > this.maxLogFiles) {
        this.info(
          `Found ${logFiles.length} log files, keeping the newest ${this.maxLogFiles}. Cleaning up older files...`
        );
        for (let i = this.maxLogFiles; i < logFiles.length; i++) {
          try {
            fs.unlinkSync(logFiles[i]!.path);
            // Use console.log directly as logger might not be fully initialized or could log excessively here
            console.log(
              `Deleted old log file: ${logFiles[i]!.name} (${formatBytes(logFiles[i]!.stats.size)})`
            );
          } catch (unlinkErr) {
            console.error(`Failed to delete old log file ${logFiles[i]!.name}:`, unlinkErr);
          }
        }
      }
    } catch (err) {
      console.error('Error cleaning up old log files:', err);
    }
  }

  /**
   * Get the current log file path
   * @returns {string | null} - The path to the current log file or null
   */
  getLogFilePath(): string | null {
    this.updateLogFile(); // Ensure the path is current
    return this.currentLogFile;
  }
}

// Export a single instance of the logger
const logger = new Logger({
  consoleLevel: DEFAULT_CONSOLE_LOG_LEVEL, // Console level from env or default
  logToFile: true, // Enable file logging (errors only)
  logToConsole: true, // Enable console logging
  maxLogFiles: 24, // Keep 24 hourly log files
  timezone: TIMEZONE, // Use the configured timezone
  useLocalTime: true, // Use local time format instead of ISO
  showEmojis: true, // Show emojis in logs
  compactMode: false, // Use standard mode by default
});

export default logger;
