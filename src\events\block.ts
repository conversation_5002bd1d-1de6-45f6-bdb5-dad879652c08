import { ActionRowBuilder, UserSelectMenuBuilder } from 'discord.js';
import dataManager from '../utils/dataManager';
import logger from '../utils/logger';
import { replyOrFollowUpEphemeral, handleInteractionError } from '../utils/interactionUtils';
import { EMOJI } from '../constants/emoji';

export const name = 'block';

// Called when the "Block" button is clicked (Interaction is likely already deferred)
// Ownership and cooldown are checked in interactionCreate.ts
export const execute = async (interaction, client, userChannel) => {
  // Sends the user select menu
  try {
    const blockSelect = new UserSelectMenuBuilder()
      .setCustomId(`block_select_${userChannel.id}`) // Consistent ID format
      .setPlaceholder('Select user(s) to block')
      .setMinValues(1)
      .setMaxValues(10); // Allow blocking multiple users at once

    const row: any = new ActionRowBuilder().addComponents(blockSelect);

    // Use replyOrFollowUpEphemeral as interaction is deferred by the central handler
    await replyOrFollowUpEphemeral(interaction, {
      content: 'Select user(s) to block from accessing your channel:',
      components: [row],
    });
  } catch (error) {
    logger.error(`Error sending block select menu: ${error.message}`);
    // Let the central handler manage the error reply
    await handleInteractionError('block.execute', error, interaction, client);
  }
};

// Called when users are selected from the menu sent above
export const handleSelectMenu = async (interaction, client, targetChannel) => {
  const menuSource = `block.handleSelectMenu:selectMenu:${interaction.customId}`;
  // Cooldown & ownership checked in interactionCreate.ts before calling this
  if (!interaction.isUserSelectMenu()) return; // Type guard

  try {
    const selectedUsers = interaction.users; // Map<string, User>
    const ownerId = interaction.user.id;
    const blockedUserTags: string[] = [];
    let errorsOccurred = false;

    logger.debug(
      `${menuSource}: User ${ownerId} selected ${selectedUsers.size} user(s) to block from channel ${targetChannel.id}`
    );

    // Fetch settings (assuming async operation)
    const settings = (await dataManager.getUserSettings(ownerId)) || { blockedUsers: [] };
    settings.blockedUsers = settings.blockedUsers || [];

    for (const [selectedUserId, selectedUser] of selectedUsers) {
      if (selectedUserId === ownerId) {
        logger.warn(`${menuSource}: Owner ${ownerId} attempted to block themselves.`);
        await replyOrFollowUpEphemeral(interaction, {
          content: `${EMOJI[client.user.id].CROSS} You cannot block yourself.`,
        });
        return; // Stop processing
      }

      try {
        if (!settings.blockedUsers.includes(selectedUserId)) {
          settings.blockedUsers.push(selectedUserId);
        }

        await targetChannel.permissionOverwrites.edit(
          selectedUserId,
          {
            ViewChannel: false,
            Connect: false,
            Speak: false,
          },
          `Blocked by channel owner ${interaction.user.tag}`
        );

        const member = await interaction.guild.members.fetch(selectedUserId).catch(() => null);
        if (member && member.voice.channelId === targetChannel.id) {
          await member.voice
            .disconnect(`Blocked by channel owner ${interaction.user.tag}`)
            .catch(disconnectError => {
              logger.warn(
                `${menuSource}: Failed to disconnect blocked user ${selectedUserId}: ${disconnectError.message}`
              );
            });
        }
        blockedUserTags.push(selectedUser.tag);
        logger.info(
          `User ${selectedUser.tag} (${selectedUserId}) blocked from channel ${targetChannel.id} by owner ${ownerId}`
        );
      } catch (userBlockError) {
        logger.error(
          `${menuSource}: Failed to block user ${selectedUserId} (${selectedUser.tag}): ${userBlockError.message}`
        );
        errorsOccurred = true;
      }
    }

    // Save updated settings (assuming async operation)
    // await dataManager.setUserSettings(ownerId, settings);
    logger.debug(`Updated blocked users settings for owner ${ownerId}`);

    let replyMessage = '';
    if (blockedUserTags.length > 0) {
      replyMessage += `${EMOJI[client.user.id].CHECK} Blocked ${blockedUserTags.join(', ')}.`;
    }
    if (errorsOccurred) {
      replyMessage +=
        (replyMessage ? '\n' : '') +
        '⚠️ Errors occurred while blocking some users. Check bot permissions.';
    }
    if (!replyMessage) {
      replyMessage = 'No users were blocked (possibly due to errors or selecting yourself).';
    }

    // Final reply is handled here, clearing components
    await replyOrFollowUpEphemeral(interaction, {
      content: replyMessage,
      components: [],
    });
  } catch (error) {
    logger.error(`${menuSource}: Failed to handle block select menu: ${error.message}`);
    // Use central handler for error reporting
    await handleInteractionError(menuSource, error, interaction, client);
  }
};
