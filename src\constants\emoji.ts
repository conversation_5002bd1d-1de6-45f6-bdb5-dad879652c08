interface UserPermissions {
  WAITING_ROOM: string;
  UNTRUST: string;
  UNBLOCK: string;
  TRUST: string;
  REGION: string;
  PURGE: string;
  PRIVACY: string;
  OWNERSHIP: string;
  NAME: string;
  LIMIT: string;
  KICK: string;
  JOIN: string;
  INVITE: string;
  DELETE: string;
  CROSS: string;
  CLAIM: string;
  CHECK: string;
  BLOCK: string;
  PUBLIC: string;
  LOCKED: string;
  HIDDEN: string;
  SUPPORT: string;
  SETUP: string;
}

interface PermissionsMap {
  [id: string]: UserPermissions;
}

export const EMOJI: PermissionsMap = {
  // MyVc
  '668825091030777857': {
    WAITING_ROOM: '<:waiting_room:1363403888866230303>',
    UNTRUST: '<:untrust:1363403875297530037>',
    UNBLOCK: '<:unblock:1363403838580854885>',
    TRUST: '<:trust:1363403827486658781>',
    REGION: '<:region:1363403809682096231>',
    PURGE: '<:purge:1363403798290235614>',
    PRIVACY: '<:privacy:1363403777671168132>',
    OWNERSHIP: '<:ownership:1363403763917914243>',
    NAME: '<:name:1363403752417263719>',
    LIMIT: '<:limit:1363403742002679969>',
    KICK: '<:kick:1363403730946494524>',
    JOIN: '<:join:1363403715675029635>',
    INVITE: '<:invite:1363403483885338764>',
    DELETE: '<:delete:1363403467405791332>',
    CROSS: '<:cross:1363403455171006534>',
    CLAIM: '<:claim:1363403437261197312>',
    CHECK: '<:check:1363403422916673716>',
    BLOCK: '<:block:1363403408794456064>',
    PUBLIC: '<:public:1364841934362775583>',
    LOCKED: '<:locked:1364843101293907998>',
    HIDDEN: '<:hidden:1364841883146387497>',
    SUPPORT: '<:help:1366373835921821799>',
    SETUP: '<:setup:1366804136540242000>',
  },
  // TestVCBot
  '1272578873577705472': {
    WAITING_ROOM: '<:waiting_room:1363403888866230303>',
    UNTRUST: '<:untrust:1363403875297530037>',
    UNBLOCK: '<:unblock:1363403838580854885>',
    TRUST: '<:trust:1363403827486658781>',
    REGION: '<:region:1363403809682096231>',
    PURGE: '<:purge:1363403798290235614>',
    PRIVACY: '<:privacy:1363403777671168132>',
    OWNERSHIP: '<:ownership:1363403763917914243>',
    NAME: '<:name:1363403752417263719>',
    LIMIT: '<:limit:1363403742002679969>',
    KICK: '<:kick:1363403730946494524>',
    JOIN: '<:join:1363403715675029635>',
    INVITE: '<:invite:1363403483885338764>',
    DELETE: '<:delete:1363403467405791332>',
    CROSS: '<:cross:1363403455171006534>',
    CLAIM: '<:claim:1363403437261197312>',
    CHECK: '<:check:1363403422916673716>',
    BLOCK: '<:block:1363403408794456064>',
    PUBLIC: '<:public:1364841934362775583>',
    LOCKED: '<:locked:1364843101293907998>',
    HIDDEN: '<:hidden:1364841883146387497>',
    SUPPORT: '<:hidden:1364841883146387497>',
    SETUP: '<:hidden:1364841883146387497>',
  },
  //
  '1127584574566842388': {
    WAITING_ROOM: '<:waiting_room:1363209969368961234>',
    UNTRUST: '<:untrust:1363383022279331961>',
    UNBLOCK: '<:unblock:1363383008660422706>',
    TRUST: '<:trust:1363209957377441876>',
    REGION: '<:region:1363382987781046342>',
    PURGE: '<:purge:1363403798290235614>',
    PRIVACY: '<:privacy:1363209947189346314>',
    OWNERSHIP: '<:ownership:1363382971096109237>',
    NAME: '<:name:1363209936766501145>',
    LIMIT: '<:limit:1363209923022028818>',
    KICK: '<:kick:1363382946857357342>',
    JOIN: '<:join:1363403715675029635>',
    INVITE: '<:invite:1363382928205414530>',
    DELETE: '<:delete:1363382914259091526>',
    CROSS: '<:cross:1363393014441185411>',
    CLAIM: '<:claim:1363384236324814949>',
    CHECK: '<:check:1363393002151612427>',
    BLOCK: '<:block:1363382902041219072>',
    PUBLIC: '<:public:1364842129863475200>',
    LOCKED: '<:locked:1364843212216340552>',
    HIDDEN: '<:hidden:1364842179872296981>',
    SUPPORT: '<:help:1365959242720215091>',
    SETUP: '<:setup:1366795553165283359>',
  },
};
