/**
 * Reload Command
 * Allows developers to reload commands without restarting the bot
 */
import { EmbedBuilder } from 'discord.js';
import { Message, Client, Collection } from 'discord.js';
import fs from 'fs';
import path from 'path';
import logger from '../utils/logger';
import { EMOJI } from '../constants/emoji';

// Define a basic interface for the expected command structure (similar to commands.ts)
interface TextCommand {
  name: string;
  description?: string;
  usage?: string;
  devOnly?: boolean;
  adminOnly?: boolean;
  execute: (message: Message, args: string[], client: Client) => Promise<any>;
}

// Augment the discord.js Client interface if not already done globally
// It's safer to include it here in case this file is used independently,
// or ensure it's in a central types file.
declare module 'discord.js' {
  interface Client {
    textCommands: Collection<string, TextCommand>;
  }
}

export const name = 'reload';
export const description = 'Reloads one or all commands';
export const usage = 'reload [command_name]';
export const devOnly = true; // Only developers can use this command
export const adminOnly = false;

export const execute = async (message: Message, args: string[], client: Client) => {
  try {
    // Check if a specific command was provided
    if (args.length > 0) {
      const commandName = args[0].toLowerCase();

      // Check if command exists
      if (!client.textCommands.has(commandName)) {
        return message.reply({
          content: `${EMOJI[client.user.id].CROSS} Command \`${commandName}\` not found.`,
          allowedMentions: { repliedUser: false },
        });
      }

      // Reload the specific command
      const result = global.reloadCommand(commandName);

      if (result.success) {
        logger.info(`Command ${commandName} reloaded by ${message.author.tag}`);
        return message.reply({
          content: `${EMOJI[client.user.id].CHECK} Successfully reloaded command \`${commandName}\`.`,
          allowedMentions: { repliedUser: false },
        });
      } else {
        logger.error(`Failed to reload command ${commandName}:`, result.message);
        return message.reply({
          content: `${EMOJI[client.user.id].CROSS} Failed to reload command \`${commandName}\`: ${result.message}`,
          allowedMentions: { repliedUser: false },
        });
      }
    } else {
      // Reload all commands
      const textCommandsPath = path.join(__dirname, '../textCommands');
      const textCommandFiles = fs
        .readdirSync(textCommandsPath)
        .filter(file => file.endsWith('.ts'));

      const results = {
        success: [],
        failed: [],
      };

      // Clear commands collection
      client.textCommands.clear();

      // Reload each command
      for (const file of textCommandFiles) {
        try {
          const commandName = file.split('.')[0];
          const result = global.loadCommand(commandName);

          if (result.success) {
            results.success.push(commandName);
          } else {
            results.failed.push({ name: commandName, reason: result.message });
          }
        } catch (error) {
          logger.error(`Error reloading command from ${file}:`, error);
          results.failed.push({ name: file.split('.')[0], reason: error.message });
        }
      }

      // Create results embed
      const embed = new EmbedBuilder()
        .setColor(results.failed.length === 0 ? 0x00ff00 : 0xffff00)
        .setTitle('Command Reload Results')
        .setDescription(
          `Reloaded ${results.success.length} commands, ${results.failed.length} failed`
        )
        .setFooter({
          text: `Requested by ${message.author.tag}`,
          iconURL: message.author.displayAvatarURL(),
        })
        .setTimestamp();

      // Add successful reloads
      if (results.success.length > 0) {
        embed.addFields({
          name: `${EMOJI[client.user.id].CHECK} Successfully Reloaded`,
          value: results.success.map(cmd => `\`${cmd}\``).join(', '),
        });
      }

      // Add failed reloads
      if (results.failed.length > 0) {
        embed.addFields({
          name: `${EMOJI[client.user.id].CROSS} Failed to Reload`,
          value: results.failed.map(f => `\`${f.name}\`: ${f.reason}`).join('\n'),
        });
      }

      logger.info(
        `All commands reloaded by ${message.author.tag}: ${results.success.length} succeeded, ${results.failed.length} failed`
      );

      return message.reply({ embeds: [embed], allowedMentions: { repliedUser: false } });
    }
  } catch (error) {
    logger.error('Error in reload command:', error);
    message
      .reply({
        content: 'An error occurred while reloading commands.',
        allowedMentions: { repliedUser: false },
      })
      .catch(err => logger.error('Error sending error message:', err));
  }
};
