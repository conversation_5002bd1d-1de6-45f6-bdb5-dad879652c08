import { Modal<PERSON>uilder, TextInputBuilder, TextInputStyle, ActionRowBuilder } from 'discord.js';
import logger from '../utils/logger';
import { replyOrFollowUpEphemeral, handleInteractionError } from '../utils/interactionUtils';
import { EMOJI } from '../constants/emoji';

export const name = 'limit';

// Called when the "Limit" button is clicked
// Interaction is NOT deferred here because showModal is used.
// Ownership & cooldown checked in interactionCreate.ts
export const execute = async (interaction, client, userChannel) => {
  // Shows the modal to the user
  try {
    const modal = new ModalBuilder()
      .setCustomId(`limit_modal_${userChannel.id}`) // Consistent ID format
      .setTitle('Set User Limit');

    const input = new TextInputBuilder()
      .setCustomId('user_limit') // Consistent field ID
      .setLabel('User Limit (0-99)')
      .setPlaceholder('0 = unlimited, max 99')
      .setRequired(true)
      .setStyle(TextInputStyle.Short)
      .setMaxLength(2)
      .setMinLength(1);

    const actionRow: any = new ActionRowBuilder().addComponents(input);
    modal.addComponents(actionRow);

    await interaction.showModal(modal);
    // No reply needed here, handled by handleModalSubmit
    // No return needed
  } catch (error) {
    logger.error(`Error showing limit modal: ${error.message}`);
    // Try to send an error reply if modal wasn't shown
    await handleInteractionError('limit.execute', error, interaction, client);
  }
};

// Called when the modal created above is submitted
export const handleModalSubmit = async (interaction, client, targetChannel) => {
  const modalSource = `limit.handleModalSubmit:modal:${interaction.customId}`;
  // Interaction is deferred in interactionCreate.ts before calling this
  // Cooldown & ownership also checked before calling this
  try {
    const newLimitStr = interaction.fields.getTextInputValue('user_limit');
    const newLimit = parseInt(newLimitStr, 10);
    logger.debug(
      `${modalSource}: User ${interaction.user.tag} submitted new limit "${newLimitStr}" (parsed: ${newLimit}) for channel ${targetChannel.id}`
    );

    if (isNaN(newLimit) || newLimit < 0 || newLimit > 99) {
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].CROSS} Invalid limit. Please enter a number between 0 and 99.`,
      });
      return;
    }

    await targetChannel.setUserLimit(newLimit, `Set by owner ${interaction.user.tag}`);
    const limitText = newLimit === 0 ? 'unlimited' : newLimit;
    logger.info(
      `Channel ${targetChannel.id} user limit set to ${limitText} by ${interaction.user.tag}`
    );

    // Optional: Save user preference (assuming async)
    try {
      const userId = interaction.user.id;
      // await dataManager.setUserSetting(userId, 'channelLimitPreference', newLimit);
      logger.debug(`Updated limit preference for user ${userId}`);
    } catch (settingError) {
      logger.error(
        `Failed to save limit preference for user ${interaction.user.id}: ${settingError}`
      );
    }

    // Final reply handled here
    await replyOrFollowUpEphemeral(interaction, {
      content: `${EMOJI[client.user.id].CHECK} User limit set to ${limitText} ${newLimit === 1 ? 'user' : 'users'}.`,
    });
  } catch (error) {
    logger.error(`${modalSource}: Failed to handle limit modal submission: ${error.message}`);
    await handleInteractionError(modalSource, error, interaction, client);
  }
};
