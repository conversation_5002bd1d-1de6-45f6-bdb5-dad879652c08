/**
 * List Command
 * Lists all servers the bot is in with essential information and pagination buttons
 * Developer-only command
 */
import {
  EmbedBuilder,
  Message,
  Client,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  ComponentType
} from 'discord.js';
import logger from '../utils/logger';
import { EMOJI } from '../constants/emoji';

export const name = 'list';
export const description = 'Lists all servers the bot is in with essential information';
export const usage = 'list [page]';
export const devOnly = true; // Only developers can use this command
export const adminOnly = false;

// Number of servers to show per page
const SERVERS_PER_PAGE = 8; // Reduced for cleaner display

// Simple emojis for the compact display
const EMOJIS = {
  OWNER: '👑',
  MEMBERS: '👥',
  INVITE: '🔗',
};

export const execute = async (message: Message, args: string[], client: Client) => {
  try {
    // Get all guilds and sort them by member count (largest first)
    const guilds = Array.from(client.guilds.cache.values())
      .sort((a, b) => b.memberCount - a.memberCount);

    // Calculate total pages
    const totalPages = Math.ceil(guilds.length / SERVERS_PER_PAGE);

    // Parse page number from args
    let page = 1;
    if (args.length > 0) {
      const parsedPage = parseInt(args[0]);
      if (!isNaN(parsedPage) && parsedPage > 0 && parsedPage <= totalPages) {
        page = parsedPage;
      }
    }

    // Send the server list with pagination
    await sendServerListPage(message, guilds, page, totalPages, client);
  } catch (error) {
    logger.error('Error in list command:', error);

    // Try to send an error message with fallbacks
    try {
      // First try to reply to the message
      return message.reply({
        content: `${EMOJI[client.user.id]?.CROSS || '❌'} An error occurred while listing servers: ${error.message}`,
        allowedMentions: { repliedUser: false },
      }).catch(async (replyError) => {
        logger.error(`Failed to send error reply: ${replyError.message}`);

        // If reply fails, try to DM the user
        try {
          await message.author.send({
            content: `I couldn't respond in the channel. An error occurred while listing servers: ${error.message}`
          });
        } catch (dmError) {
          logger.error(`Failed to send DM to user: ${dmError.message}`);
        }
      });
    } catch (finalError) {
      logger.error(`Complete failure in error handling: ${finalError.message}`);
    }
  }
};

/**
 * Send a server list page with pagination buttons
 * @param message The message that triggered the command
 * @param guilds Array of all guilds
 * @param page Current page number
 * @param totalPages Total number of pages
 * @param client The Discord client
 */
async function sendServerListPage(message: Message, guilds: any[], page: number, totalPages: number, client: Client) {
  try {
    // Calculate start and end indices for the current page
    const startIndex = (page - 1) * SERVERS_PER_PAGE;
    const endIndex = Math.min(startIndex + SERVERS_PER_PAGE, guilds.length);
    const pageGuilds = guilds.slice(startIndex, endIndex);

    // Create the embed with simplified information
    const embed = new EmbedBuilder()
      .setColor(0x5865f2)
      .setTitle(`🌐 Server List`)
      .setDescription(`**${guilds.length}** servers total • Page **${page}**/**${totalPages}**`)
      .setFooter({
        text: `Requested by ${message.author.tag}`,
        iconURL: message.author.displayAvatarURL(),
      })
      .setTimestamp();

    // Add simplified server information
    let serverList = '';
    for (let i = 0; i < pageGuilds.length; i++) {
      const guild = pageGuilds[i];
      const index = startIndex + i + 1;

      try {
        // Get owner information (just mention, not full tag)
        const owner = await guild.fetchOwner().catch(() => null);
        const ownerMention = owner ? `<@${owner.id}>` : 'Unknown';

        // Get invite link
        let inviteLink = 'None';
        try {
          const botMember = guild.members.cache.get(client.user.id);
          if (botMember && botMember.permissions.has('ManageGuild')) {
            const invites = await guild.invites.fetch();
            if (invites.size > 0) {
              const firstInvite = invites.first();
              if (firstInvite) {
                inviteLink = `discord.gg/${firstInvite.code}`;
              }
            }
          }
        } catch (error) {
          // Silently fail
        }

        // Format server entry
        serverList += `**${index}.** ${guild.name}\n`;
        serverList += `${EMOJIS.OWNER} ${ownerMention} • ${EMOJIS.MEMBERS} ${guild.memberCount} • ${EMOJIS.INVITE} ${inviteLink}\n\n`;
      } catch (error) {
        serverList += `**${index}.** ${guild.name || 'Unknown Server'}\n`;
        serverList += `❌ Error loading server info\n\n`;
      }
    }

    embed.setDescription(`**${guilds.length}** servers total • Page **${page}**/**${totalPages}**\n\n${serverList}`);

    // Create pagination buttons
    const buttons = [];

    // Previous button
    buttons.push(
      new ButtonBuilder()
        .setCustomId(`list_prev_${page}_${totalPages}`)
        .setLabel('Previous')
        .setEmoji('⬅️')
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(page === 1)
    );

    // Page indicator button (disabled)
    buttons.push(
      new ButtonBuilder()
        .setCustomId('list_page_indicator')
        .setLabel(`${page}/${totalPages}`)
        .setStyle(ButtonStyle.Primary)
        .setDisabled(true)
    );

    // Next button
    buttons.push(
      new ButtonBuilder()
        .setCustomId(`list_next_${page}_${totalPages}`)
        .setLabel('Next')
        .setEmoji('➡️')
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(page === totalPages)
    );

    const row = new ActionRowBuilder<ButtonBuilder>().addComponents(buttons);

    // Send the message
    const response = await message.reply({
      embeds: [embed],
      components: totalPages > 1 ? [row] : [],
      allowedMentions: { repliedUser: false },
    });

    // Set up button collector if there are multiple pages
    if (totalPages > 1) {
      const collector = response.createMessageComponentCollector({
        componentType: ComponentType.Button,
        time: 300000, // 5 minutes
      });

      collector.on('collect', async (interaction) => {
        if (interaction.user.id !== message.author.id) {
          await interaction.reply({
            content: 'Only the command user can navigate pages.',
            ephemeral: true,
          });
          return;
        }

        const customId = interaction.customId;
        let newPage = page;

        if (customId.startsWith('list_prev_')) {
          newPage = Math.max(1, page - 1);
        } else if (customId.startsWith('list_next_')) {
          newPage = Math.min(totalPages, page + 1);
        }

        if (newPage !== page) {
          await interaction.deferUpdate();
          // Update the message with the new page
          await updateServerListPage(interaction, guilds, newPage, totalPages, client);
        }
      });

      collector.on('end', async () => {
        // Disable all buttons when collector expires
        const disabledRow = new ActionRowBuilder<ButtonBuilder>().addComponents(
          buttons.map(button => ButtonBuilder.from(button).setDisabled(true))
        );

        try {
          await response.edit({ components: [disabledRow] });
        } catch (error) {
          // Ignore errors if message was deleted
        }
      });
    }
  } catch (error) {
    logger.error('Error sending server list page:', error);
    throw error;
  }
}

/**
 * Update a server list page (for button interactions)
 */
async function updateServerListPage(interaction: any, guilds: any[], page: number, totalPages: number, client: Client) {
  // This is similar to sendServerListPage but for updates
  const startIndex = (page - 1) * SERVERS_PER_PAGE;
  const endIndex = Math.min(startIndex + SERVERS_PER_PAGE, guilds.length);
  const pageGuilds = guilds.slice(startIndex, endIndex);

  const embed = new EmbedBuilder()
    .setColor(0x5865f2)
    .setTitle(`🌐 Server List`)
    .setFooter({
      text: `Requested by ${interaction.user.tag}`,
      iconURL: interaction.user.displayAvatarURL(),
    })
    .setTimestamp();

  // Add simplified server information
  let serverList = '';
  for (let i = 0; i < pageGuilds.length; i++) {
    const guild = pageGuilds[i];
    const index = startIndex + i + 1;

    try {
      const owner = await guild.fetchOwner().catch(() => null);
      const ownerMention = owner ? `<@${owner.id}>` : 'Unknown';

      let inviteLink = 'None';
      try {
        const botMember = guild.members.cache.get(client.user.id);
        if (botMember && botMember.permissions.has('ManageGuild')) {
          const invites = await guild.invites.fetch();
          if (invites.size > 0) {
            const firstInvite = invites.first();
            if (firstInvite) {
              inviteLink = `discord.gg/${firstInvite.code}`;
            }
          }
        }
      } catch (error) {
        // Silently fail
      }

      serverList += `**${index}.** ${guild.name}\n`;
      serverList += `${EMOJIS.OWNER} ${ownerMention} • ${EMOJIS.MEMBERS} ${guild.memberCount} • ${EMOJIS.INVITE} ${inviteLink}\n\n`;
    } catch (error) {
      serverList += `**${index}.** ${guild.name || 'Unknown Server'}\n`;
      serverList += `❌ Error loading server info\n\n`;
    }
  }

  embed.setDescription(`**${guilds.length}** servers total • Page **${page}**/**${totalPages}**\n\n${serverList}`);

  // Create updated pagination buttons
  const buttons = [
    new ButtonBuilder()
      .setCustomId(`list_prev_${page}_${totalPages}`)
      .setLabel('Previous')
      .setEmoji('⬅️')
      .setStyle(ButtonStyle.Secondary)
      .setDisabled(page === 1),
    new ButtonBuilder()
      .setCustomId('list_page_indicator')
      .setLabel(`${page}/${totalPages}`)
      .setStyle(ButtonStyle.Primary)
      .setDisabled(true),
    new ButtonBuilder()
      .setCustomId(`list_next_${page}_${totalPages}`)
      .setLabel('Next')
      .setEmoji('➡️')
      .setStyle(ButtonStyle.Secondary)
      .setDisabled(page === totalPages)
  ];

  const row = new ActionRowBuilder<ButtonBuilder>().addComponents(buttons);

  await interaction.editReply({
    embeds: [embed],
    components: [row],
  });
}