import {
  Interaction,
  MessageFlags,
  VoiceChannel,
  PermissionFlagsBits,
  EmbedBuilder,
} from 'discord.js';
import logger from '../utils/logger';
// Import the new utility functions
import { getUserVoiceChannel } from '../utils/channelUtils';
import {
  handleInteractionError,
  deferReplyEphemeral,
  replyOrFollowUpEphemeral,
} from '../utils/interactionUtils';

// Import button handlers
import * as blockHandler from './block';
import * as claimHandler from './claim';
import * as deleteHandler from './delete';
import * as inviteHandler from './invite';
import * as kickHandler from './kick';
import * as limitHandler from './limit';
import * as nameHandler from './name';
import * as privacyHandler from './privacy';
import * as regionHandler from './region';
import * as transferHandler from './transfer';
import * as trustHandler from './trust';
import * as unblockHandler from './unblock';
import * as untrustHandler from './untrust';
import * as waitingHandler from './waiting';
import * as channelWelcomeHandler from './channelWelcome';
import { EMOJI } from '../constants/emoji';
import { tempChannelRepository } from '../data/repositories/tempChannel';
import { execute as setupExecute } from '../commands/setup';

/**
 * Comprehensive cooldown management system
 *
 * Discord rate limits:
 * - Global rate limit: 50 requests per second
 * - Per-route limits (e.g., channel creation, message sending)
 * - Per-resource limits (e.g., guild, channel)
 *
 * We implement tiered cooldowns:
 * - TIER 1 (Light): Simple actions with minimal API impact (3s)
 * - TIER 2 (Moderate): Actions that use multiple API calls (5s)
 * - TIER 3 (Heavy): Resource-intensive actions (10s)
 * - TIER 4 (Critical): Actions that should be limited severely (15s)
 */

// Cooldown management
const cooldowns = new Map();

// Cooldown tiers in seconds
const COOLDOWN_TIERS = {
  LIGHT: 3, // Low-impact actions
  MODERATE: 5, // Medium-impact actions
  HEAVY: 10, // High-impact actions
  CRITICAL: 15, // Very high-impact actions that should be rate-limited strictly
};

// Action-specific cooldowns
const ACTIONS_COOLDOWN = {
  // Simple actions (minimal API impact)
  name: COOLDOWN_TIERS.LIGHT,
  limit: COOLDOWN_TIERS.LIGHT,
  ping: COOLDOWN_TIERS.LIGHT,

  // Moderate actions (multiple API calls)
  privacy: COOLDOWN_TIERS.MODERATE,
  region: COOLDOWN_TIERS.MODERATE,
  trust: COOLDOWN_TIERS.MODERATE,
  untrust: COOLDOWN_TIERS.MODERATE,
  kick: COOLDOWN_TIERS.MODERATE,
  invite: COOLDOWN_TIERS.MODERATE,
  waiting: COOLDOWN_TIERS.MODERATE,

  // Heavy actions (resource-intensive)
  block: COOLDOWN_TIERS.HEAVY,
  unblock: COOLDOWN_TIERS.HEAVY,
  claim: COOLDOWN_TIERS.HEAVY,
  transfer: COOLDOWN_TIERS.HEAVY,

  // Critical actions (should be limited severely)
  delete: COOLDOWN_TIERS.CRITICAL,

  // Default for all other interactions
  default: COOLDOWN_TIERS.LIGHT,
};

// Global cooldown tracking for overall bot usage
const GLOBAL_LIMIT = 45; // Keep well under Discord's 50/s limit
const globalRequests = {
  timestamps: [],
  count: 0,
};

/**
 * Check global rate limit
 * @returns {boolean} True if under limit, false if rate limited
 */
function checkGlobalLimit() {
  const now = Date.now();

  // Remove timestamps older than 1 second
  globalRequests.timestamps = globalRequests.timestamps.filter(time => now - time < 1000);

  // Check if we're over the limit
  if (globalRequests.timestamps.length >= GLOBAL_LIMIT) {
    return false;
  }

  // Add the current timestamp
  globalRequests.timestamps.push(now);
  globalRequests.count++;

  // Every 1000 requests, log stats
  if (globalRequests.count % 1000 === 0) {
    logger.info(`Global API usage: ${globalRequests.count} requests made so far`);
  }

  return true;
}

/**
 * Check cooldown for a specific user and action
 * @param {string} userId - User ID
 * @param {string} action - Action name
 * @param {string} [channelId] - Optional channel ID for per-channel limits
 * @returns {number} Seconds remaining on cooldown, or 0 if not on cooldown
 */
function checkCooldown(userId, action, channelId = null) {
  // First check the global rate limit
  if (!checkGlobalLimit()) {
    return 1; // Return a small cooldown if we're hitting global limit
  }

  const now = Date.now();
  const userCooldowns = cooldowns.get(userId) || {};

  // Create a key that combines action and channel if provided
  const cooldownKey = channelId ? `${action}-${channelId}` : action;

  // Get the cooldown time for this action (or default)
  const cooldownAmount = (ACTIONS_COOLDOWN[action] || ACTIONS_COOLDOWN.default) * 1000;

  // Check if the user is on cooldown for this action
  if (userCooldowns[cooldownKey] && now < userCooldowns[cooldownKey] + cooldownAmount) {
    const timeLeft = (userCooldowns[cooldownKey] + cooldownAmount - now) / 1000;
    return Math.round(timeLeft);
  }

  // No cooldown, so update the timestamp
  userCooldowns[cooldownKey] = now;
  cooldowns.set(userId, userCooldowns);
  return 0;
}

/**
 * Clean up old cooldowns periodically to prevent memory leaks
 */
setInterval(() => {
  const now = Date.now();

  // For each user's cooldowns
  for (const [userId, userCooldowns] of cooldowns.entries()) {
    // Find expired cooldowns
    const expiredActions = [];
    for (const [action, timestamp] of Object.entries(userCooldowns) as any) {
      // If the cooldown has expired (using the longest possible cooldown time)
      if (now > timestamp + COOLDOWN_TIERS.CRITICAL * 1000) {
        expiredActions.push(action);
      }
    }

    // Remove expired cooldowns
    for (const action of expiredActions) {
      delete userCooldowns[action];
    }

    // If the user has no more cooldowns, remove them from the map
    if (Object.keys(userCooldowns).length === 0) {
      cooldowns.delete(userId);
    }
  }

  // Log cooldown stats occasionally
  logger.debug(`Cooldown cleanup: ${cooldowns.size} users with active cooldowns`);
}, 60000); // Run every minute

// --- Handler Maps ---
const buttonHandlers = {
  block: blockHandler,
  claim: claimHandler, // Note: Claim might need special handling (no userChannel needed initially)
  delete: deleteHandler, // Typically requires confirmation
  invite: inviteHandler,
  kick: kickHandler,
  limit: limitHandler, // Opens a modal
  name: nameHandler, // Opens a modal
  privacy: privacyHandler,
  region: regionHandler,
  transfer: transferHandler, // Typically requires confirmation
  trust: trustHandler,
  unblock: unblockHandler,
  untrust: untrustHandler,
  waiting: waitingHandler,
  purge: channelWelcomeHandler, // Add the purge button handler
  // Note: Confirmation buttons (e.g., 'delete_confirm_...', 'transfer_confirm_...') might need separate handling or be managed within the initial handler
};

const selectMenuHandlers = {
  // Action mapped to the handler module
  block: blockHandler, // Assuming blockHandler.handleSelectMenu exists
  unblock: unblockHandler, // Assuming unblockHandler.handleSelectMenu exists
  trust: trustHandler, // Assuming trustHandler.handleSelectMenu exists
  untrust: untrustHandler, // Assuming untrustHandler.handleSelectMenu exists
  kick: kickHandler, // Assuming kickHandler.handleSelectMenu exists
  invite: inviteHandler, // Assuming inviteHandler.handleSelectMenu exists
  privacy: privacyHandler, // Assuming privacyHandler.handleSelectMenu exists
  region: regionHandler, // Assuming regionHandler.handleSelectMenu exists
  transfer: transferHandler,
  waiting: waitingHandler, // Assuming waitingHandler.handleSelectMenu exists
  // Add other select menu actions as needed
};

const modalSubmitHandlers = {
  // Action mapped to the handler module
  name: nameHandler, // Assuming nameHandler.handleModalSubmit exists
  limit: limitHandler, // Assuming limitHandler.handleModalSubmit exists
  purge: channelWelcomeHandler, // Add the purge modal handler
  // Add other modal actions as needed
};

export const name = 'interactionCreate';
export const execute = async (interaction, client) => {
  const source = `interaction:${interaction.id}:${interaction.type}`;

  try {
    if (!checkGlobalLimit()) {
      logger.warn(`Global rate limit triggered by interaction ${interaction.id}`);
      return;
    }

    logger.debug(`Received interaction: ${interaction.type} from ${interaction.user.tag}`);

    // Command handling
    if (interaction.isChatInputCommand()) {
      const command = client.commands.get(interaction.commandName);
      if (!command) {
        logger.error(`No command matching ${interaction.commandName} was found.`);
        await replyOrFollowUpEphemeral(interaction, {
          content: `No command matching \`${interaction.commandName}\` was found.`,
        });
        return;
      }

      const userId = interaction.user.id;
      const commandName = interaction.commandName;
      const cooldownRemaining = checkCooldown(userId, commandName);
      if (cooldownRemaining > 0) {
        logger.debug(
          `User ${interaction.user.tag} on cooldown for command ${commandName} (${cooldownRemaining}s)`
        );
        await replyOrFollowUpEphemeral(interaction, {
          content: `⏳ Please wait ${cooldownRemaining} second(s) before using this command again.`,
        });
        return;
      }
      try {
        await command.execute(interaction, client);
      } catch (error) {
        await handleInteractionError(
          `${source}:command:${commandName}`,
          error,
          interaction,
          client
        );
      }
      return; // Exit after handling command
    }

    // Button handling
    else if (interaction.isButton()) {
      const buttonId = interaction.customId;
      const buttonSource = `${source}:button:${buttonId}`;
      logger.debug(`Button clicked: ${buttonId} by ${interaction.user.tag}`);

      // console.log('action details ', action, handler);

      if (buttonId.startsWith('start_setup')) {
        // Check for required permissions (Administrator or Manage Guild)
        if (
          !interaction.memberPermissions.has(PermissionFlagsBits.Administrator) &&
          !interaction.memberPermissions.has(PermissionFlagsBits.ManageGuild)
        ) {
          const embed = new EmbedBuilder()
            .setColor(0xe74c3c) // red
            .setDescription(
              `${EMOJI[client.user.id].CROSS} You need Administrator or Manage Server permission to use this setup button.`
            );

          await interaction.reply({
            embeds: [embed],
            flags: MessageFlags.Ephemeral,
          });
          return;
        }

        setupExecute(interaction, client);
        return;
      }

      if (buttonId.startsWith('transfer_confirm_')) {
        // Handle transfer confirmation
        const targetUserId = buttonId.split('_')[3]; // Get the target user ID from the custom ID
        const channelId = buttonId.split('_')[2];
        // Verify new owner exists
        const userId = interaction.user.id;
        const newOwner = await interaction.guild.members.fetch(targetUserId).catch(() => null);
        if (!newOwner) {
          await interaction.editReply({
            content: `${EMOJI[client.user.id].UNTRUST} The selected user is no longer in the server.`,
            flags: MessageFlags.SuppressEmbeds,
          });
          return;
        }

        // Check if the new owner is in the voice channel
        if (newOwner.voice.channelId !== channelId) {
          await interaction.editReply({
            content: `${EMOJI[client.user.id].UNTRUST} The selected user must be in the voice channel to receive ownership.`,
            flags: MessageFlags.SuppressEmbeds,
          });
          return;
        }

        // Transfer ownership in the collection
        client.tempChannels.set(channelId, targetUserId);

        // Fetch the channel
        const channel = await interaction.guild.channels.fetch(channelId).catch(() => null);
        if (!channel) {
          await interaction.editReply({
            content: `${EMOJI[client.user.id].UNTRUST} Channel no longer exists.`,
            flags: MessageFlags.SuppressEmbeds,
          });
          return;
        }

        // Update channel permissions for the new owner
        try {
          // Check if bot has necessary permissions before attempting to modify permissions
          const botMember = interaction.guild.members.me;
          const botPermissions = channel.permissionsFor(botMember);

          if (!botPermissions.has('ManageRoles') || !botPermissions.has('ManageChannels')) {
            logger.warn(
              `Bot lacks required permissions to update channel permissions in ${channel.name} (${channelId})`
            );
            await interaction.editReply({
              content: `${EMOJI[client.user.id].UNTRUST} Channel ownership has been transferred to ${newOwner.user.tag}, but I couldn't update channel permissions due to missing bot permissions. The new owner may need to ask an admin to adjust permissions.`,
              flags: MessageFlags.SuppressEmbeds,
            });
            return;
          }

          // Update permissions in two steps to ensure success
          // Step 1: Grant new owner all necessary permissions first - with PROPER ERROR HANDLING
          try {
            await tempChannelRepository.setNewOwnerOfTempChanel(channelId, targetUserId);

            await channel.permissionOverwrites.create(
              targetUserId,
              {
                ViewChannel: true,
                Connect: true,
                Speak: true,
                // MuteMembers: true,
                // DeafenMembers: true,
                // MoveMembers: true,
                // Stream: true,
                // UseEmbeddedActivities: true,
              },
              { reason: `Channel ownership transferred from ${interaction.user.tag}` }
            );

            logger.debug(
              `Successfully updated permissions for new owner ${newOwner.user.tag} in channel ${channel.name}`
            );
          } catch (permError) {
            logger.error(`Failed to update permissions for new owner: ${permError.message}`);
            // Continue with ownership transfer even if permissions update fails
          }

          // Step 2: Modify original owner's permissions - with PROPER ERROR HANDLING
          if (userId !== targetUserId) {
            // Avoid changing permissions if transferring to self
            try {
              await channel.permissionOverwrites.edit(userId, {
                MuteMembers: null,
                DeafenMembers: null,
                MoveMembers: null,
              });

              logger.debug(
                `Successfully updated permissions for previous owner ${interaction.user.tag} in channel ${channel.name}`
              );
            } catch (permError) {
              logger.error(`Failed to update permissions for previous owner: ${permError.message}`);
              // Continue with ownership transfer even if permissions update fails
            }
          }

          // Get current channel settings
          const currentOwnerSettings = client.userSettings.get(userId);
          const newOwnerSettings = client.userSettings.get(targetUserId);

          // Initial response to user
          interaction.deferUpdate().then(() =>
            interaction.editReply({
              content: `${EMOJI[client.user.id].CHECK} Channel ownership has been transferred to ${newOwner.user.tag}. Checking for user configuration...`,
              flags: MessageFlags.SuppressEmbeds,
            })
          );

          // Wait 5 seconds before applying new owner settings
          setTimeout(async () => {
            try {
              // Fetch fresh channel data to make sure it still exists
              const updatedChannel = await interaction.guild.channels
                .fetch(channelId)
                .catch(() => null);
              if (!updatedChannel) {
                logger.warn(`Channel ${channelId} no longer exists after ownership transfer`);
                return;
              }

              // Fetch the new owner's current settings (which might have been updated)
              const refreshedNewOwnerSettings = client.userSettings.get(targetUserId);

              // Re-check permissions before applying settings
              const updatedBotPermissions = updatedChannel.permissionsFor(
                interaction.guild.members.me
              );
              const canManageChannel = updatedBotPermissions.has('ManageChannels');

              if (refreshedNewOwnerSettings) {
                // Apply new owner's settings to the channel
                logger.info(
                  `Applying ${newOwner.user.tag}'s personal settings to transferred channel ${channelId}`
                );

                // Apply settings that require ManageChannels permission
                if (canManageChannel) {
                  // Apply name if set
                  if (refreshedNewOwnerSettings.name) {
                    await updatedChannel.setName(refreshedNewOwnerSettings.name).catch(err => {
                      logger.error(`Failed to set name for transferred channel: ${err.message}`);
                    });
                  }

                  // Apply user limit if set
                  if (typeof refreshedNewOwnerSettings.userLimit !== 'undefined') {
                    await updatedChannel
                      .setUserLimit(refreshedNewOwnerSettings.userLimit)
                      .catch(err => {
                        logger.error(
                          `Failed to set user limit for transferred channel: ${err.message}`
                        );
                      });
                  }

                  // Apply region if set
                  if (refreshedNewOwnerSettings.rtcRegion) {
                    await updatedChannel
                      .setRTCRegion(refreshedNewOwnerSettings.rtcRegion)
                      .catch(err => {
                        logger.error(
                          `Failed to set region for transferred channel: ${err.message}`
                        );
                      });
                  }
                } else {
                  logger.warn(
                    `Cannot apply channel settings requiring ManageChannels permission for ${newOwner.user.tag}'s channel`
                  );
                }

                // Apply permissions-related settings if possible
                if (updatedBotPermissions.has('ManageRoles')) {
                  // Apply privacy settings if needed
                  if (refreshedNewOwnerSettings.isPrivate) {
                    // Make the channel private
                    await updatedChannel.permissionOverwrites
                      .edit(interaction.guild.roles.everyone, {
                        ViewChannel: false,
                      })
                      .catch(err => {
                        logger.error(
                          `Failed to set privacy for transferred channel: ${err.message}`
                        );
                      });
                  }

                  // Apply locked settings if needed
                  if (refreshedNewOwnerSettings.isLocked) {
                    // Lock the channel
                    await updatedChannel.permissionOverwrites
                      .edit(interaction.guild.roles.everyone, {
                        Connect: false,
                      })
                      .catch(err => {
                        logger.error(`Failed to lock transferred channel: ${err.message}`);
                      });
                  }
                } else {
                  logger.warn(
                    `Cannot apply permission settings for ${newOwner.user.tag}'s channel due to missing ManageRoles permission`
                  );
                }

                logger.info(
                  `Successfully applied ${newOwner.user.tag}'s settings to transferred channel ${channelId}`
                );
              } else if (currentOwnerSettings && !newOwnerSettings) {
                // If new owner doesn't have settings, create them with basic channel settings from previous owner
                client.userSettings.set(targetUserId, {
                  name: currentOwnerSettings.name,
                  userLimit: currentOwnerSettings.userLimit,
                  rtcRegion: currentOwnerSettings.rtcRegion,
                  isPrivate: currentOwnerSettings.isPrivate,
                  isLocked: currentOwnerSettings.isLocked,
                  trustedUsers: [], // Start with fresh trusted users
                  blockedUsers: [], // Start with fresh blocked users
                });
                global.saveUserSettings(targetUserId);
                logger.info(
                  `Created new settings for ${newOwner.user.tag} based on the transferred channel`
                );
              } else {
                logger.info(
                  `New owner ${newOwner.user.tag} has no settings, keeping channel as is`
                );
              }

              // Update the message to confirm completion
              await interaction
                .editReply({
                  content: `✅ Channel ownership has been transferred to ${newOwner.user.tag} and their settings have been applied.`,
                  flags: MessageFlags.SuppressEmbeds,
                })
                .catch(() => {
                  // Message may have expired, that's fine
                });
            } catch (applyError) {
              logger.error(`Error applying settings after transfer: ${applyError.message}`);
            }
          }, 5000); // 5 second delay
        } catch (error) {
          logger.error(`Error during ownership transfer: ${error.message}`);
          // Even if permissions update fails, ownership is still transferred in the collection
          await interaction.editReply({
            content: `${EMOJI[client.user.id].CHECK} Channel ownership has been transferred to ${newOwner.user.tag} in the bot's database, but there was an error updating channel permissions. The channel will function normally but you may need admin help to adjust permissions.`,
            flags: MessageFlags.SuppressEmbeds,
          });
        }
        return;
      }

      if (buttonId.startsWith('purge_button_')) {
        await channelWelcomeHandler.handlePurgeButton(interaction, client);
        return;
      }

      // Special handling for ping refresh button
      if (buttonId === 'refresh_ping') {
        // This button is handled by the collector in the ping command
        // No need to do anything here, just return to avoid the handler check
        logger.debug(
          `${buttonSource}: Refresh ping button clicked by ${interaction.user.tag} - handled by collector`
        );
        return;
      }

      // --- Special Button Handling (Cancel, Join) ---
      if (buttonId.startsWith('cancel_')) {
        await replyOrFollowUpEphemeral(interaction, {
          content: `${EMOJI[client.user.id].CHECK} Action cancelled.`,
          components: [],
        });
        return;
      }

      // Handle delete confirmation
      if (buttonId.startsWith('delete_confirm_')) {
        const confirmSource = `${source}:button:${buttonId}`;
        const channelId = buttonId.split('_')[2];

        // Double-check ownership at the time of confirmation
        const targetChannel = await interaction.guild?.channels.fetch(channelId).catch(() => null);
        if (!targetChannel || !(targetChannel instanceof VoiceChannel)) {
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].CROSS} Channel not found.`,
            components: [],
          });
          return;
        }

        const isOwner = client.tempChannels.get(targetChannel.id) === interaction.user.id;
        if (!isOwner) {
          logger.warn(
            `${confirmSource}: User ${interaction.user.tag} attempted delete confirmation without ownership.`
          );
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].CROSS} You are no longer the owner of this channel.`,
            components: [],
          });
          return;
        }

        // Check cooldown again for the critical delete action
        const cooldownRemaining = checkCooldown(interaction.user.id, 'delete', targetChannel.id);
        if (cooldownRemaining > 0) {
          await replyOrFollowUpEphemeral(interaction, {
            content: `⏳ Please wait ${cooldownRemaining}s before deleting.`,
            components: [],
          });
          return;
        }

        try {
          logger.info(
            `User ${interaction.user.tag} confirmed deletion for channel ${targetChannel.name} (${channelId})`
          );

          await targetChannel.delete(`Deleted by owner ${interaction.user.tag}`);
          client.tempChannels.delete(channelId);
          logger.info(`Channel ${channelId} deleted successfully.`);
          interaction.deferUpdate().then(() =>
            interaction.editReply({
              content: `${EMOJI[client.user.id].CHECK} Channel has been deleted.`,
              components: [],
            })
          );
        } catch (deleteError) {
          logger.error(
            `${confirmSource}: Failed to delete channel ${channelId}: ${deleteError.message}`
          );
          await handleInteractionError(confirmSource, deleteError, interaction, client);
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].CROSS} Failed to delete the channel. The bot might lack permissions or the channel was already deleted.`,
            components: [],
          });
        }
        return; // Handled delete confirmation
      }

      if (buttonId.startsWith('join_')) {
        // Defer join button specifically
        if (!interaction.deferred) {
          const deferred = await deferReplyEphemeral(interaction);
          if (!deferred) {
            logger.warn(`${buttonSource}: Interaction expired before deferral for join button.`);
            return;
          }
        }
        // ... existing join button logic using replyOrFollowUpEphemeral ...
        const channelId = buttonId.split('_')[1];
        let channel = null;
        try {
          channel = await interaction.guild?.channels.fetch(channelId).catch(() => null);
          if (!channel && !interaction.inGuild()) {
            logger.warn(
              `${buttonSource}: Join button used in DM, target guild/channel resolution needed.`
            );
            await replyOrFollowUpEphemeral(interaction, {
              content: `${EMOJI[client.user.id].UNTRUST} Cannot join channel from DMs using this button. Please join the server first.`,
            });
            return;
          }
        } catch (fetchError) {
          logger.error(`${buttonSource}: Error fetching channel ${channelId}: ${fetchError}`);
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].CROSS} Error finding the channel.`,
          });
          return;
        }

        if (!channel) {
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].CROSS} This channel no longer exists.`,
          });
          return;
        }

        const channelUrl = `https://discord.com/channels/${channel.guild.id}/${channelId}`;
        try {
          const member = await channel.guild.members.fetch(interaction.user.id).catch(() => null);
          if (!member) {
            await replyOrFollowUpEphemeral(interaction, {
              content: `${EMOJI[client.user.id].CROSS} You need to join the server first. Direct link: ${channelUrl}`,
            });
            return;
          }
          if (!member.voice.channel) {
            await replyOrFollowUpEphemeral(interaction, {
              content: `${EMOJI[client.user.id].CROSS} Please join a voice channel first. Direct link: ${channelUrl}`,
            });
            return;
          }
          await member.voice.setChannel(channel);
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].CHECK} Moved to ${channel.name}.`,
          });
        } catch (moveError) {
          logger.error(
            `${buttonSource}: Error moving user ${interaction.user.id} to ${channelId}: ${moveError}`
          );
          await handleInteractionError(`${buttonSource}:moveUser`, moveError, interaction, client);
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].CROSS} Could not move you. Try joining manually: ${channelUrl}`,
          });
        }
        return;
      }

      // --- General Button Logic ---
      const needsDeferral = !['name', 'limit'].includes(buttonId); // Modals are handled by button click, defer later
      if (needsDeferral) {
        const deferred = await deferReplyEphemeral(interaction);
        if (!deferred) {
          logger.warn(`${buttonSource}: Interaction expired before deferral.`);
          return;
        }
      }

      try {
        // Get user channel (required for most button actions)
        // Some buttons don't need a voice channel
        let userChannel = null;

        // Special handling for buttons that don't need a voice channel
        if (buttonId !== 'claim' && buttonId !== 'refresh_ping') {
          userChannel = await getUserVoiceChannel(interaction.user.id, interaction.guild, client);

          // Check if userChannel exists before trying to access its properties
          if (!userChannel) {
            // If the button has a channel ID in its custom ID, check if it's still valid
            const parts = buttonId.split('_');
            if (parts.length >= 3) {
              const channelId = parts[2];
              // Check if this channel is in our tracking but no longer exists
              if (client.tempChannels.has(channelId)) {
                // Try to fetch the channel to confirm it's gone
                const channel = await interaction.guild?.channels
                  .fetch(channelId)
                  .catch(() => null);
                if (!channel) {
                  // Channel is gone, remove from tracking
                  client.tempChannels.delete(channelId);
                  // Save the updated tempChannels map
                  try {
                    global.saveTempChannels?.();
                  } catch (saveError) {
                    logger.error(
                      `Failed to save temp channels after deletion: ${saveError.message}`
                    );
                  }

                  logger.info(
                    `${buttonSource}: Removed deleted channel ${channelId} from tracking`
                  );
                }
              }
            }

            logger.debug(
              `${buttonSource}: User ${interaction.user.tag} not in relevant voice channel.`
            );
            await replyOrFollowUpEphemeral(interaction, {
              content: `${EMOJI[client.user.id].UNTRUST} You need to be in your temporary voice channel (or own one) to use this button.`,
            });
            return;
          }

          // Check ownership
          const isOwner = client.tempChannels.get(userChannel.id) === interaction.user.id;
          if (!isOwner) {
            logger.warn(
              `${userChannel.id}: User ${interaction.user.tag} interacted with menu for channel ${userChannel.name} without ownership.`
            );
            await replyOrFollowUpEphemeral(interaction, {
              content: `${EMOJI[client.user.id].UNTRUST} You are not the owner of this channel.`,
            });
            return;
          }
        }

        // Determine the action name (usually the button ID itself)
        const action = buttonId; // May need adjustment for confirmation buttons
        const handler = buttonHandlers[action];

        if (handler && typeof handler.execute === 'function') {
          // Only apply cooldowns to channel setting buttons
          const settingButtons = ['name', 'limit', 'privacy', 'region', 'waiting'];
          const needsCooldown = settingButtons.includes(action);
          const cooldownRemaining = needsCooldown
            ? checkCooldown(interaction.user.id, action, userChannel?.id)
            : 0;

          if (cooldownRemaining > 0) {
            logger.debug(
              `${buttonSource}: User ${interaction.user.tag} on cooldown for action ${action} (${cooldownRemaining}s)`
            );
            await replyOrFollowUpEphemeral(interaction, {
              content: `⏳ Please wait ${cooldownRemaining} second(s) before using this action again.`,
            });
            return;
          }

          // Check ownership if required by the action
          const ownerActions = [
            'delete',
            'transfer',
            'privacy',
            'name',
            'limit',
            'region',
            'waiting',
          ]; // Add other owner-only actions

          // Non-owner actions that don't need ownership checks
          const nonOwnerActions = ['claim', 'refresh_ping'];

          if (userChannel && ownerActions.includes(action) && !nonOwnerActions.includes(action)) {
            const isOwner = client.tempChannels.get(userChannel.id) === interaction.user.id;
            if (!isOwner) {
              logger.warn(
                `${buttonSource}: User ${interaction.user.tag} attempted owner action ${action} on channel ${userChannel.id} without ownership.`
              );
              await replyOrFollowUpEphemeral(interaction, {
                content: `${EMOJI[client.user.id].UNTRUST} You must be the owner of this channel to perform this action.`,
              });
              return;
            }
          }

          // Execute the handler
          // The handler is responsible for its own specific logic, including sending final replies or modals
          await handler.execute(interaction, client, userChannel);
        } else {
          logger.warn(`${buttonSource}: No handler found for button ID: ${buttonId}`);
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].UNTRUST} This button is not configured correctly.`,
          });
        }
      } catch (error) {
        await handleInteractionError(buttonSource, error, interaction, client);
      }
    }

    // Select Menu Handling
    else if (interaction.isAnySelectMenu()) {
      const menuId = interaction.customId;
      const menuSource = `${source}:selectMenu:${menuId}`;
      logger.debug(`Select menu interaction: ${menuId} by ${interaction.user.tag}`);

      // Defer reply
      const deferred = await deferReplyEphemeral(interaction);
      if (!deferred) {
        logger.warn(`${menuSource}: Interaction expired before deferral.`);
        return;
      }

      try {
        // Extract action and channel ID (Format: action_select_channelId)
        const parts = menuId.split('_');
        const action = parts[0];
        const channelId = parts.slice(2).join('_');

        if (!action || !channelId) {
          logger.error(
            `${menuSource}: Could not extract action or channel ID from custom ID: ${menuId}`
          );
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].CROSS} Invalid interaction configuration (ID format error).`,
          });
          return;
        }

        // Fetch the target channel
        const targetChannel = await interaction.guild?.channels.fetch(channelId).catch(() => null);
        if (!targetChannel || !(targetChannel instanceof VoiceChannel)) {
          logger.warn(
            `${menuSource}: Target channel ${channelId} not found or not a voice channel.`
          );
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].CROSS} The target channel no longer exists or is invalid.`,
          });
          return;
        }

        // Verify ownership (most select menus modify the channel)
        const isOwner = client.tempChannels.get(targetChannel.id) === interaction.user.id;
        if (!isOwner) {
          logger.warn(
            `${menuSource}: User ${interaction.user.tag} interacted with menu for channel ${channelId} without ownership.`
          );
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].UNTRUST} You are not the owner of this channel.`,
          });
          return;
        }

        // No cooldowns for select menus

        // Route to the handler
        const handler = selectMenuHandlers[action];
        // Assume handlers have a specific function, e.g., handleSelectMenu
        if (handler && typeof handler.handleSelectMenu === 'function') {
          // Execute the handler
          // The handler is responsible for processing values and sending the final reply
          await handler.handleSelectMenu(interaction, client, targetChannel);
        } else {
          logger.warn(`${menuSource}: No select menu handler found for action: ${action}`);
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].UNTRUST} This select menu action is not configured correctly.`,
          });
        }
      } catch (error) {
        await handleInteractionError(menuSource, error, interaction, client);
      }
    }

    // Modal Submit Handling
    else if (interaction.isModalSubmit()) {
      const modalId = interaction.customId;
      const modalSource = `${source}:modal:${modalId}`;
      logger.debug(`Modal submitted: ${modalId} by ${interaction.user.tag}`);

      // Defer reply
      const deferred = await deferReplyEphemeral(interaction);
      if (!deferred) {
        logger.warn(`${modalSource}: Interaction expired before deferral.`);
        return;
      }

      try {
        // Extract action and channel ID (Format: action_modal_channelId)
        const parts = modalId.split('_');
        const action = parts[0];
        const channelId = parts.slice(2).join('_');

        if (!action || !channelId) {
          logger.error(
            `${modalSource}: Could not extract action or channel ID from custom ID: ${modalId}`
          );
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].CROSS} Invalid interaction configuration (ID format error).`,
          });
          return;
        }

        // Fetch the target channel
        const targetChannel = await interaction.guild?.channels.fetch(channelId).catch(error => {
          logger.error(
            `${modalSource}: Could not fetch channel ${channelId} for modal submission: ${error.message}`
          );
          return null;
        });

        if (!targetChannel || !(targetChannel instanceof VoiceChannel)) {
          // Channel might have been deleted, remove it from tracking
          if (client.tempChannels.has(channelId)) {
            client.tempChannels.delete(channelId);
            // Save the updated tempChannels map
            try {
              global.saveTempChannels?.();
            } catch (saveError) {
              logger.error(`Failed to save temp channels after deletion: ${saveError.message}`);
            }
          }

          logger.warn(
            `${modalSource}: Target channel ${channelId} not found or not a voice channel.`
          );
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].CROSS} The target channel no longer exists or is invalid.`,
          });
          return;
        }

        // Verify ownership (modals modify the channel)
        const isOwner = client.tempChannels.get(targetChannel.id) === interaction.user.id;
        if (!isOwner) {
          logger.warn(
            `${modalSource}: User ${interaction.user.tag} submitted modal for channel ${channelId} without ownership.`
          );
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].UNTRUST} You are not the owner of this channel.`,
          });
          return;
        }

        // No cooldowns for modal submissions

        if (modalId.startsWith('purge_modal_')) {
          await channelWelcomeHandler.handlePurgeModalSubmit(interaction, client);
          return;
        }

        // Route to the handler
        const handler = modalSubmitHandlers[action];
        // Assume handlers have a specific function, e.g., handleModalSubmit
        if (handler && typeof handler.handleModalSubmit === 'function') {
          // Execute the handler
          // The handler is responsible for processing fields and sending the final reply
          await handler.handleModalSubmit(interaction, client, targetChannel);
        } else {
          logger.warn(`${modalSource}: No modal submit handler found for action: ${action}`);
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].CROSS} This modal action is not configured correctly.`,
          });
        }
      } catch (error) {
        await handleInteractionError(modalSource, error, interaction, client);
      }
    }

    // --- Add Autocomplete Handling if needed ---
    // else if (interaction.isAutocomplete()) { ... }
  } catch (error) {
    await handleInteractionError(`${source}:toplevel`, error, interaction, client);
  }
};
