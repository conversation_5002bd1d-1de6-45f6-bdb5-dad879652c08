import {
  ActionRow<PERSON>uilder,
  StringSelectMenuBuilder,
  ButtonBuilder,
  ButtonStyle,
  EmbedBuilder,
} from 'discord.js';
import logger from '../utils/logger';
import { replyOrFollowUpEphemeral, handleInteractionError } from '../utils/interactionUtils';
import { EMOJI } from '../constants/emoji';

export const name = 'transfer';

// Called when the "Transfer" button is clicked
// Interaction is deferred, ownership & cooldown checked in interactionCreate.ts
export const execute = async (interaction, client, userChannel) => {
  // Sends the user select menu
  try {
    // Get users currently in the channel (excluding the owner) to select from
    const membersInChannel = userChannel.members
      .filter(member => member.id !== interaction.user.id)
      .map(member => ({ label: member.user.tag, value: member.id }));

    if (membersInChannel.length === 0) {
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].UNTRUST} No other users are in your channel to transfer ownership to.`,
      });
      return;
    }

    const transferSelect = new StringSelectMenuBuilder()
      .setCustomId(`transfer_select_${userChannel.id}`) // Consistent ID format
      .setPlaceholder('Select a user to transfer ownership to')
      .setMinValues(1)
      .setMaxValues(1) // Only transfer to one user at a time
      .addOptions(membersInChannel);

    const row: any = new ActionRowBuilder().addComponents(transferSelect);

    // Use replyOrFollowUpEphemeral as interaction is deferred by the central handler
    await replyOrFollowUpEphemeral(interaction, {
      content:
        'Select a user currently **in this channel** to transfer ownership to. You will lose owner permissions.',
      components: [row],
    });
  } catch (error) {
    logger.error(`Error sending transfer select menu: ${error.message}`);
    await handleInteractionError('transfer.execute', error, interaction, client);
  }
};

// Called when a user is selected from the menu sent above
export const handleSelectMenu = async (interaction, client, targetChannel) => {
  const menuSource = `transfer.handleSelectMenu:selectMenu:${interaction.customId}`;
  // Cooldown & ownership checked in interactionCreate.ts before calling this
  if (!interaction.isStringSelectMenu()) return; // Type guard

  try {
    const selectedValue = interaction.values[0]; // Get the selected user ID
    const currentOwnerId = interaction.user.id;

    if (!selectedValue) {
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].CROSS} No user selected.`,
        components: [],
      });
      return;
    }

    const targetUserId = selectedValue;

    // Fetch the target member to verify they are still in the channel
    const targetMember = await interaction.guild.members.fetch(targetUserId).catch(() => null);
    const selectedUser = targetMember?.user;

    if (!targetMember || !selectedUser) {
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].UNTRUST} The selected user is no longer in this server.`,
        components: [],
      });
      return;
    }

    logger.debug(
      `${menuSource}: User ${currentOwnerId} selected ${selectedUser.tag} (${targetUserId}) for ownership transfer of channel ${targetChannel.id}`
    );

    if (targetMember.voice.channelId !== targetChannel.id) {
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].UNTRUST} The selected user must be in the voice channel to receive ownership.`,
        components: [],
      });
      return;
    }

    // --- Proceed with Transfer --- (Requires Confirmation Step)
    // The actual transfer logic is complex (updating tempChannels map, swapping permissions,
    // applying new owner's settings) and CRITICAL. It should happen *after* a confirmation.
    // This handler should send the confirmation message.

    const confirmButton = new ButtonBuilder()
      .setCustomId(`transfer_confirm_${targetChannel.id}_${targetUserId}`) // Include target user ID
      .setLabel(`Transfer`)
      .setStyle(ButtonStyle.Primary)
      .setEmoji(EMOJI[client.user.id].CHECK);

    const cancelButton = new ButtonBuilder()
      .setCustomId(`cancel_${targetChannel.id}`)
      .setLabel('Cancel')
      .setStyle(ButtonStyle.Secondary)
      .setEmoji(EMOJI[client.user.id].CROSS);

    const row: any = new ActionRowBuilder().addComponents(confirmButton, cancelButton);

    await replyOrFollowUpEphemeral(interaction, {
      content: `🚨 **Confirm Transfer**: Are you sure you want to transfer ownership of **${targetChannel.name}** to **${selectedUser.tag}**? You will lose owner privileges.`, // Added names
      components: [row],
    });
  } catch (error) {
    logger.error(`${menuSource}: Failed to handle transfer select menu: ${error.message}`);
    await handleInteractionError(menuSource, error, interaction, client);
  }
};

/**
 * Handles actual transfer confirmation including updating ownership in client.tempChannels,
 * updating permissions, and applying the new owner's configuration from MongoDB
 *
 * @param {Interaction} interaction - The interaction from the button click
 * @param {Client} client - The Discord client
 * @param {string} channelId - The channel ID being transferred
 * @param {string} newOwnerId - The ID of the new owner
 */
export const handleTransferConfirmation = async (interaction, client, channelId, newOwnerId) => {
  const transferSource = `transfer.handleTransferConfirmation:button:${interaction.customId}`;

  try {
    // Get the channel and validate it exists
    const targetChannel = await interaction.guild?.channels.fetch(channelId).catch(() => null);
    if (!targetChannel) {
      logger.warn(`${transferSource}: Target channel ${channelId} not found.`);
      const errorEmbed = new EmbedBuilder()
        .setColor('#FF0000')
        .setTitle(`${EMOJI[client.user.id].CROSS} Channel Not Found`)
        .setDescription('The target channel no longer exists.')
        .setTimestamp();

      await interaction.update({
        embeds: [errorEmbed],
        components: [],
      });
      return;
    }

    // Get current owner ID from the tempChannels map
    const oldOwnerId = client.tempChannels.get(channelId);
    if (!oldOwnerId) {
      logger.warn(`${transferSource}: Channel ${channelId} has no owner in tempChannels map.`);
      const errorEmbed = new EmbedBuilder()
        .setColor('#FF0000')
        .setTitle(`${EMOJI[client.user.id].CROSS} Transfer Error`)
        .setDescription('This channel is not properly registered in the system.')
        .setTimestamp();

      await interaction.update({
        embeds: [errorEmbed],
        components: [],
      });
      return;
    }

    // Verify the new owner is still in the channel
    const newOwnerMember = await interaction.guild.members.fetch(newOwnerId).catch(() => null);
    if (!newOwnerMember || newOwnerMember.voice.channelId !== channelId) {
      logger.warn(`${transferSource}: New owner ${newOwnerId} is no longer in the channel.`);
      const errorEmbed = new EmbedBuilder()
        .setColor('#FF0000')
        .setTitle(`${EMOJI[client.user.id].CROSS} Transfer Failed`)
        .setDescription('The new owner must be in the voice channel to receive ownership.')
        .setTimestamp();

      await interaction.update({
        embeds: [errorEmbed],
        components: [],
      });
      return;
    }

    // 1. Update the tempChannels map with the new owner
    client.tempChannels.set(channelId, newOwnerId);
    logger.info(
      `${transferSource}: Ownership of channel ${channelId} transferred from ${oldOwnerId} to ${newOwnerId}`
    );

    // 2. Update MongoDB with the new owner information
    try {
      // Assuming we have a MongoDB model/collection for channels
      // This part will need to be adjusted based on your actual MongoDB implementation
      await client.db.updateChannelOwner(channelId, newOwnerId);
      logger.info(`${transferSource}: Updated channel owner in database for channel ${channelId}`);
    } catch (dbError) {
      logger.error(
        `${transferSource}: Failed to update database for channel ${channelId}: ${dbError.message}`
      );
      // Continue with the transfer even if DB update fails, but log the error
    }

    // 3. Reset channel to default settings - clear old owner's configuration
    try {
      // Reset to default channel settings
      logger.info(`${transferSource}: Resetting channel ${channelId} to default configuration`);

      // Reset name to a basic format if needed
      const defaultName = `${newOwnerMember.user.username}'s channel`;
      await targetChannel.setName(defaultName);

      // Reset other channel settings to defaults
      await targetChannel.setUserLimit(0); // No user limit
      await targetChannel.setBitrate(64000); // Default bitrate
      // Reset any other customizable settings

      logger.info(`${transferSource}: Successfully reset channel configuration to defaults`);
    } catch (resetError) {
      logger.error(
        `${transferSource}: Failed to reset channel configuration: ${resetError.message}`
      );
      // Non-critical, continue with the transfer
    }

    // 4. Fetch the new owner's configuration from MongoDB
    let newOwnerConfig = null;
    try {
      // This should be adjusted based on your actual MongoDB implementation
      newOwnerConfig = await client.db.getUserConfig(newOwnerId);
      logger.info(`${transferSource}: Retrieved config for new owner ${newOwnerId}`);
    } catch (configError) {
      logger.error(
        `${transferSource}: Failed to retrieve new owner config: ${configError.message}`
      );
      // Continue with default settings if config retrieval fails
    }

    // 5. Apply permission changes
    try {
      // Remove old owner's permissions
      await targetChannel.permissionOverwrites.edit(oldOwnerId, {
        // Remove special owner permissions
        ManageChannels: null,
        MuteMembers: null,
        MoveMembers: null,
        // Reset to default permissions or specified member permissions
      });

      // Give new owner permissions
      await targetChannel.permissionOverwrites.edit(newOwnerId, {
        // Add special owner permissions
        ManageChannels: true,
        MuteMembers: true,
        MoveMembers: true,
        // Add any other owner-specific permissions
      });

      logger.info(`${transferSource}: Updated permissions for channel ${channelId}`);
    } catch (permError) {
      logger.error(`${transferSource}: Failed to update permissions: ${permError.message}`);
      // This is critical, but we've already updated ownership in memory
      // Consider reverting the memory change if this fails?
    }

    // 6. Apply any new owner's config settings to the channel
    if (newOwnerConfig) {
      try {
        // Apply channel name format if configured
        if (newOwnerConfig.channelNameFormat) {
          const formattedName = newOwnerConfig.channelNameFormat
            .replace('{username}', newOwnerMember.user.username)
            .replace('{tag}', newOwnerMember.user.tag);
          // Add other replacements as needed

          await targetChannel.setName(formattedName);
          logger.info(`${transferSource}: Updated channel name to ${formattedName}`);
        }

        // Apply other channel settings from the new owner's config
        if (newOwnerConfig.userLimit !== undefined) {
          await targetChannel.setUserLimit(newOwnerConfig.userLimit);
        }

        if (newOwnerConfig.bitrate !== undefined) {
          await targetChannel.setBitrate(newOwnerConfig.bitrate);
        }

        // Apply any other custom settings from the config
        // This will depend on what settings your app supports

        logger.info(`${transferSource}: Applied new owner's settings to channel ${channelId}`);
      } catch (settingsError) {
        logger.error(`${transferSource}: Failed to apply new settings: ${settingsError.message}`);
        // Non-critical, continue
      }
    }

    // 7. Send success message
    const successEmbed = new EmbedBuilder()
      .setColor('#00FF00')
      .setTitle(`${EMOJI[client.user.id].CHECK} Ownership Transferred`)
      .setDescription(
        `Ownership of **${targetChannel.name}** has been transferred to <@${newOwnerId}>.`
      )
      .setTimestamp();

    await interaction.update({
      embeds: [successEmbed],
      components: [],
    });
  } catch (error) {
    logger.error(`${transferSource}: Unexpected error: ${error.message}`);

    const errorEmbed = new EmbedBuilder()
      .setColor('#FF0000')
      .setTitle(`${EMOJI[client.user.id].CROSS} Transfer Error`)
      .setDescription('An unexpected error occurred during ownership transfer.')
      .setTimestamp();

    // Try to inform the user even if an unexpected error occurred
    if (!interaction.replied && !interaction.deferred) {
      await interaction.update({
        embeds: [errorEmbed],
        components: [],
      });
    }
  }
};

// Note: Actual transfer logic (map update, permission swap, settings apply)
// should be triggered by the `transfer_confirm_{channelId}_{targetUserId}` button
// in interactionCreate.ts after re-validating ownership and target user presence.
