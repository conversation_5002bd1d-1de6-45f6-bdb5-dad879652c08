/**
 * VoiceStateUpdate Event Handler
 *
 * Handles the creation of temporary voice channels when users join the Join to Create channel.
 * Also handles saving user settings when they leave their temporary channel.
 * Optimized for handling 100+ concurrent users.
 */
import { ChannelType, PermissionFlagsBits, VoiceState } from 'discord.js';
import { JOIN_COOLDOWN } from '../constants';
import dataManager from '../utils/dataManager';
import logger from '../utils/logger';
import { sendWelcomeMessage } from './channelWelcome';

// Define a custom Discord error interface that includes the properties we need
interface DiscordError extends Error {
  code?: number;
  guild?: any;
  guildId?: string;
  channelId?: string;
  userId?: string;
}

// Discord API Error codes - based on Discord API documentation

// Channel processing queue to avoid hitting rate limits
const channelCreateQueue = [];
const channelDeleteQueue = [];
let isProcessingChannelQueue = false;

// Cooldown tracking to prevent spam
const userCooldowns = new Map();
const pendingMoves = new Map();
const channelCreationsInProgress = new Set();

// Temp channels scheduled for deletion
const scheduledDeletions = new Set();

// Utility: Debug log current environment variables once per minute
// This helps diagnose issues with channels not being created in the right place

// Add better tracking for channels that are in creation process
const activeCreations = new Set();

// Add this at the top with other constants

/**
 * Schedule a channel for deletion after it's empty
 * @param {VoiceChannel} channel - The voice channel
 * @param {Client} client - The Discord client
 */
async function scheduleChannelDeletion(channel, client) {
  try {
    // Get the channel owner
    const ownerId = client.tempChannels.get(channel.id);
    if (!ownerId) return;

    // Check if already scheduled
    if (scheduledDeletions.has(channel.id)) return;

    // Check if this is a waiting room
    const isWaitingRoom = channel.name.toLowerCase().includes('waiting-room');

    if (isWaitingRoom) {
      logger.debug(
        `Waiting room ${channel.name} (${channel.id}) is empty, tracking for 1-minute deletion`
      );
      // Add to the global emptyWaitingRooms Map with current timestamp
      global.emptyWaitingRooms.set(channel.id, Date.now());
      return; // Don't delete waiting rooms immediately, let the 1-minute timer handle it
    }

    logger.debug(
      `Channel ${channel.name} (${channel.id}) is empty, scheduling for immediate deletion`
    );
    scheduledDeletions.add(channel.id);

    // Reduced wait time to 50ms - just enough to catch accidental disconnects
    // but fast enough to feel immediate to users and handle cross-guild switches
    await new Promise(resolve => setTimeout(resolve, 50));

    // Fast check if channel still exists and is empty
    const freshChannel = await channel.guild.channels.fetch(channel.id).catch(() => null);
    if (!freshChannel) {
      // Channel already gone, clean up tracking
      scheduledDeletions.delete(channel.id);
      client.tempChannels.delete(channel.id);
      dataManager.saveTempChannels(client.tempChannels);
      userCooldowns.delete(ownerId);
      return;
    }

    // Check if still empty
    if (freshChannel.members.size > 0) {
      // Channel not empty anymore, just remove from scheduled deletions
      scheduledDeletions.delete(freshChannel.id);
      return;
    }

    // Clean up tracking before delete attempt to prevent any potential hang
    scheduledDeletions.delete(channel.id);
    client.tempChannels.delete(channel.id);
    userCooldowns.delete(ownerId);

    // Save the updated tempChannels Map to disk - but don't wait for it
    // This improves perceived performance by not blocking the deletion
    dataManager.saveTempChannels(client.tempChannels).catch(err => {
      logger.warn(`Failed to save temp channels during deletion: ${err.message}`);
    });

    // Delete the channel immediately
    try {
      await freshChannel.delete(`Automatic cleanup: Channel empty`);
      logger.info(`Successfully deleted empty channel ${channel.name} (${channel.id})`);
    } catch (error) {
      // If direct deletion fails, add to queue as fallback
      logger.warn(`Direct deletion failed, adding to queue: ${error.message}`);
      channelDeleteQueue.push({
        channel: freshChannel,
        timestamp: Date.now(),
      });

      // Start processing the queue if not already
      if (!isProcessingChannelQueue) {
        processChannelQueue();
      }
    }
  } catch (error) {
    logger.error(`Error in scheduleChannelDeletion: ${error.message}`);
    scheduledDeletions.delete(channel?.id);
  }
}

/**
 * Handle a user leaving a temporary channel
 * @param {VoiceState} state - The voice state update
 * @param {Client} client - The Discord client
 */
async function handleUserLeavingChannel(state, client) {
  if (!state || !state.channel) {
    return;
  }

  const channel = state.channel;
  const member = state.member;
  const userId = member.id;
  const channelId = channel.id;

  try {
    // Check if user is the owner of the channel
    if (client.tempChannels.has(channelId) && client.tempChannels.get(channelId) === userId) {
      // Save only basic channel settings (name, limits, etc.) but NOT trusted/blocked users
      // This is to prevent overwriting trusted/blocked users when a user leaves a channel
      // These should only be updated when users interact with trust/untrust/block/unblock buttons
      logger.debug(
        `User ${member.user.username} is the owner of channel ${channelId}, saving basic settings only`
      );

      // Get existing user settings
      let userSettings = client.userSettings.get(userId) || {};

      // Update only basic channel properties
      userSettings.name = channel.name;
      userSettings.userLimit = channel.userLimit;
      userSettings.bitrate = channel.bitrate;
      userSettings.rtcRegion = channel.rtcRegion;

      // Check if channel is private or has waiting room by examining everyone role permissions
      const everyoneRole = channel.guild.roles.everyone;
      const everyonePerms = channel.permissionOverwrites.cache.get(everyoneRole.id);

      if (everyonePerms) {
        // Check if the channel is private (View Channel permission denied for everyone)
        if (everyonePerms.deny.has(PermissionFlagsBits.ViewChannel)) {
          userSettings.isPrivate = true;
        } else {
          userSettings.isPrivate = false;
        }

        // Check if waiting room is enabled (Connect permission denied for everyone)
        if (everyonePerms.deny.has(PermissionFlagsBits.Connect)) {
          userSettings.hasWaitingRoom = true;
        } else {
          userSettings.hasWaitingRoom = false;
        }
      }

      // Update timestamp
      userSettings.lastModified = Date.now();

      // Save to cache
      client.userSettings.set(userId, userSettings);

      // Save to database
      try {
        await dataManager.saveUserSettings({ [userId]: userSettings });
        logger.debug(`Successfully saved basic settings for user ${member.user.username}`);
      } catch (error) {
        logger.warn(
          `Failed to save basic settings for user ${member.user.username}: ${error.message}`
        );
        // Fall back to queue-based saving
        global.saveUserSettings(userId);
      }

      // Check if user has a waiting room and delete it
      if (userSettings.hasWaitingRoom && userSettings.waitingRoomId) {
        try {
          logger.debug(
            `Attempting to delete waiting room for user ${member.user.username} (ID: ${userSettings.waitingRoomId})`
          );
          const waitingRoom = await state.guild.channels
            .fetch(userSettings.waitingRoomId)
            .catch(err => {
              logger.warn(
                `Could not fetch waiting room ${userSettings.waitingRoomId}: ${err.message}`
              );
              return null;
            });

          if (waitingRoom) {
            // Check if the waiting room is empty
            if (waitingRoom.members.size === 0) {
              logger.info(
                `Deleting empty waiting room ${waitingRoom.name} (${waitingRoom.id}) for user ${member.user.username}`
              );
              await waitingRoom
                .delete(`Owner left temp channel and waiting room is empty`)
                .catch(error => {
                  logger.error(`Failed to delete waiting room: ${error.message}`);
                });
              logger.debug(`Waiting room successfully deleted`);
            } else {
              // If waiting room has users, track it for 1-minute deletion
              logger.info(
                `Waiting room ${waitingRoom.name} (${waitingRoom.id}) still has users, tracking for 1-minute deletion`
              );
              global.emptyWaitingRooms.set(waitingRoom.id, Date.now());
            }
          } else {
            logger.warn(
              `Waiting room ${userSettings.waitingRoomId} not found, may have been deleted already`
            );
          }

          // Update user settings regardless of whether deletion succeeded
          userSettings.hasWaitingRoom = false;
          userSettings.waitingRoomId = null;
          client.userSettings.set(userId, userSettings);
          global.saveUserSettings(userId);
          logger.debug(
            `User settings updated: waiting room flags cleared for ${member.user.username}`
          );
        } catch (error) {
          logger.error(`Error handling waiting room deletion: ${error.message}`);
        }
      }
    }

    // Fetch the latest channel data to see if it's now empty
    const updatedChannel = await state.guild.channels.fetch(channelId).catch(() => null);
    if (!updatedChannel) {
      logger.debug(`Channel ${channelId} no longer exists, removing from tracking`);
      client.tempChannels.delete(channelId);
      dataManager.saveTempChannels(client.tempChannels);
      return;
    }

    logger.debug(`Fetching latest data for channel ${channelId}`);

    // Check if channel is empty, schedule it for deletion
    if (updatedChannel.members.size === 0) {
      await scheduleChannelDeletion(updatedChannel, client);
    }
  } catch (error) {
    logger.error(`Error handling user leaving channel: ${error.message}`);
  }
}

export const name = 'voiceStateUpdate';
export const execute = async (oldState, newState, client) => {
  try {
    // Skip if the bot is not fully initialized
    if (!client.isReady()) return;

    // Only process events for actual users (not bots)
    if (newState.member && newState.member.user.bot) return;

    // Get the guild ID
    const guildId = newState.guild.id;

    // Check if this guild has been set up before doing any processing
    // This prevents unnecessary processing and error messages for guilds without setup
    let guildConfig;
    try {
      guildConfig = await dataManager.getGuildSettings(guildId, true); // Use cache for faster checks

      // If guild has no settings or is missing required settings, skip processing entirely
      if (!guildConfig || !guildConfig.joinChannelId || !guildConfig.tempCategoryId) {
        // Only log at debug level to avoid spamming logs
        logger.debug(
          `Skipping voice state update for guild ${guildId} - not configured with MyVC yet.`
        );
        return;
      }
    } catch (err) {
      // Just skip processing if we can't get guild settings
      logger.debug(
        `Error getting guild settings for ${guildId}, skipping voice state update: ${err.message}`
      );
      return;
    }

    // Now that we know this guild is configured, log state changes for debugging
    const userName = newState.member
      ? newState.member.nickname || newState.member.user.username
      : 'unknown';
    const oldChannelId = oldState?.channelId || 'nowhere';
    const newChannelId = newState?.channelId || 'nowhere';

    logger.debug(
      `Voice state update: User ${userName} moved from ${oldChannelId} to ${newChannelId}`
    );

    // Store the guild settings values we're about to use
    let joinChannelId = guildConfig.joinChannelId;
    let tempCategoryId = guildConfig.tempCategoryId;

    // Get fresh guild settings from database to ensure we have the latest
    try {
      const freshGuildConfig = await dataManager.getGuildSettings(guildId, false);
      if (freshGuildConfig && freshGuildConfig.joinChannelId && freshGuildConfig.tempCategoryId) {
        // Update with fresh values if available
        joinChannelId = freshGuildConfig.joinChannelId;
        tempCategoryId = freshGuildConfig.tempCategoryId;
      }
    } catch (err) {
      // If we can't get fresh settings, continue with cached values
      logger.debug(`Using cached guild settings for ${guildId}: ${err.message}`);
    }

    // Log which settings are being used
    logger.debug(
      `Using server-specific settings for guild ${guildId} - joinChannelId: ${joinChannelId}, tempCategoryId: ${tempCategoryId}`
    );

    // Try to fetch the join channel and category
    const guild = newState.guild;

    // Check if the channels exist in the cache first
    let joinChannel = guild.channels.cache.get(joinChannelId);
    let category = guild.channels.cache.get(tempCategoryId);

    // If not in cache, try to fetch them
    if (!joinChannel || !category) {
      try {
        // Only fetch the specific channels we need instead of all channels
        if (!joinChannel) {
          joinChannel = await guild.channels.fetch(joinChannelId).catch(() => null);
          if (!joinChannel) {
            logger.debug(
              `Join channel with ID ${joinChannelId} not found for guild ${guildId}. Skipping voice state update.`
            );
            return;
          }
        }

        if (!category) {
          category = await guild.channels.fetch(tempCategoryId).catch(() => null);
          if (!category) {
            logger.debug(
              `Category with ID ${tempCategoryId} not found for guild ${guildId}. Skipping voice state update.`
            );
            return;
          }
        }
      } catch (error) {
        logger.debug(`Error fetching channels for guild ${guildId}: ${error.message}`);
        return;
      }
    }

    // Check if this is the Join to Create channel
    if (newState.channelId === joinChannelId) {
      const userId = newState.member.id;
      const userName = newState.member.nickname || newState.member.user.username;

      // Check if user is already in an active creation process
      if (activeCreations.has(userId) || channelCreationsInProgress.has(userId)) {
        logger.debug(
          `User ${userName} already has a channel being created, ignoring duplicate request`
        );
        return;
      }

      // Check if there's a pending move for this user
      if (pendingMoves.has(userId)) {
        const pendingMoveData = pendingMoves.get(userId);
        logger.debug(
          `Found pending move for user ${userName} to channel ${pendingMoveData.targetChannelId}, skipping channel creation`
        );
        return;
      }

      // Check if the user is on cooldown
      const now = Date.now();
      if (userCooldowns.has(userId)) {
        const cooldownEnd = userCooldowns.get(userId);
        if (now < cooldownEnd) {
          const timeRemaining = (cooldownEnd - now) / 1000;
          logger.debug(
            `User ${userName} is on cooldown for ${timeRemaining.toFixed(1)} more seconds`
          );
          return;
        }
      }

      // Add user to active creations set and apply cooldown
      activeCreations.add(userId);
      userCooldowns.set(userId, now + JOIN_COOLDOWN);
      logger.debug(`Added user ${userName} to active creations set and set cooldown`);

      // Check for existing channel
      let existingChannel = null;
      for (const [channelId, ownerId] of client.tempChannels.entries()) {
        if (ownerId === userId) {
          existingChannel = await guild.channels.fetch(channelId).catch(() => null);
          if (existingChannel) {
            logger.debug(`Found existing channel ${channelId} for ${userName}, moving user`);
            try {
              await newState.member.voice.setChannel(existingChannel);
              activeCreations.delete(userId);
              return;
            } catch (error) {
              logger.error(`Failed to move user to existing channel: ${error.message}`);
              // If we can't move them, remove the channel from tracking and create a new one
              client.tempChannels.delete(channelId);
            }
          } else {
            // Channel doesn't exist, remove from tracking
            client.tempChannels.delete(channelId);
          }
        }
      }

      // No existing channel found or couldn't move to it, create a new one
      logger.debug(`Creating new channel for ${userName}`);
      channelCreationsInProgress.add(userId);

      try {
        await handleUserJoiningChannel(newState, client, tempCategoryId);
      } catch (error) {
        logger.error(`Error handling user joining channel: ${error.message}`);
        channelCreationsInProgress.delete(userId);
        activeCreations.delete(userId);
      }
    }
    // Check if user is leaving a channel and handle it
    else if (oldState.channelId && oldState.channelId !== joinChannelId) {
      // Check if this is a temporary channel that we're tracking
      if (client.tempChannels.has(oldState.channelId)) {
        const channelOwnerId = client.tempChannels.get(oldState.channelId);
        const userId = oldState.member.id;

        logger.debug(
          `User ${userName} left channel ${oldState.channelId}. Channel owner ID: ${channelOwnerId}, User ID: ${userId}`
        );

        // Check if user is moving between channels or leaving entirely
        if (newState.channelId && pendingMoves.has(userId)) {
          const pendingMove = pendingMoves.get(userId);
          logger.debug(
            `Detected voice state update for pending move. User: ${userName}, Target: ${pendingMove.targetChannelId}, Current: ${newState.channelId}`
          );

          // If this is an automatic move we're tracking, don't process further
          if (
            pendingMove.targetChannelId === newState.channelId ||
            // Also check for "double move" which can happen with Discord
            // Discord sometimes first moves to a temporary channel before the final one
            (newState.channel && newState.channel.parentId === tempCategoryId)
          ) {
            logger.debug(
              `User ${userName} was moved automatically to channel ${newState.channelId}, not processing further`
            );
            return;
          }
        }

        try {
          // Handle the user leaving a temporary channel
          await handleUserLeavingChannel(oldState, client);
        } catch (error) {
          logger.error(`Error handling user leaving channel: ${error.message}`);
        }
      }
    }
  } catch (error) {
    handleError('voiceStateUpdate', error);
  }
};

/**
 * Queue a channel creation request
 * @param {VoiceState} state - The voice state
 * @param {Client} client - The Discord client
 * @param {string} categoryId - Category ID for the new channel
 */
function queueChannelCreation(state, client, categoryId) {
  // Add to the queue
  channelCreateQueue.push({
    state,
    client,
    categoryId,
    timestamp: Date.now(),
  });

  // Start processing the queue if not already
  if (!isProcessingChannelQueue) {
    logger.debug(`Added channel creation to queue (size now: ${channelCreateQueue.length})`);
    processChannelQueue();
  } else {
    logger.debug(
      `Added channel creation to queue while processing (size now: ${channelCreateQueue.length})`
    );
  }
}

/**
 * Process the channel creation and deletion queue
 * This helps prevent hitting Discord API rate limits
 */
async function processChannelQueue() {
  if (isProcessingChannelQueue) return;
  isProcessingChannelQueue = true;

  try {
    logger.debug(
      `Starting to process channel queue. Create: ${channelCreateQueue.length}, Delete: ${channelDeleteQueue.length}`
    );

    // Process channel deletions first - prioritize cleanup for better UX
    if (channelDeleteQueue.length > 0) {
      logger.debug(`Processing ${channelDeleteQueue.length} channel deletions first`);

      // Process deletions in batches of 5 for better performance
      const batchSize = 5;
      while (channelDeleteQueue.length > 0) {
        const batch = channelDeleteQueue.splice(0, batchSize);
        const deletionPromises = batch.map(task => {
          return task.channel
            .delete(`Automatic cleanup: Channel empty`)
            .then(() => {
              logger.info(
                `Successfully deleted empty channel ${task.channel.name} (${task.channel.id})`
              );
              return true;
            })
            .catch(error => {
              logger.error(`Error deleting channel ${task.channel.id}: ${error.message}`);
              return false;
            });
        });

        // Wait for all deletions in this batch to complete
        await Promise.allSettled(deletionPromises);

        // Small delay between batches to avoid rate limits
        if (channelDeleteQueue.length > 0) {
          await new Promise(resolve => setTimeout(resolve, 50));
        }
      }
    }

    // Process channel creations with optimized delay
    while (channelCreateQueue.length > 0) {
      const task = channelCreateQueue.shift();
      logger.debug(
        `Processing channel creation for ${task.state.member.user.username} (queue size now: ${channelCreateQueue.length})`
      );

      try {
        await createTemporaryChannel(task.state, task.client, task.categoryId);
      } catch (error) {
        logger.error(`Error creating temporary channel: ${error.message}`);
        // Remove user from in-progress tracking
        if (task.state.member && task.state.member.id) {
          channelCreationsInProgress.delete(task.state.member.id);
        }
      }

      // Reduced delay between channel creations to 50ms
      if (channelCreateQueue.length > 0) {
        await new Promise(resolve => setTimeout(resolve, 50));
      }
    }
  } finally {
    isProcessingChannelQueue = false;
  }
}

/**
 * Handle a user joining the Join to Create channel
 * Creates a new temporary channel for them by adding the request to the processing queue
 *
 * This function doesn't directly create the channel, but adds the request to a queue
 * that's processed by createTemporaryChannel to avoid hitting Discord rate limits
 *
 * @param {VoiceState} state - The voice state update
 * @param {Client} client - The Discord client
 * @param {string} categoryId - The category ID for the new channel
 */
async function handleUserJoiningChannel(state, client, categoryId) {
  try {
    const member = state.member;
    if (!member) {
      logger.error('Cannot create channel: member is null or undefined');
      return;
    }

    const userName = member.nickname || member.user.username;

    // Log the permissions the bot has in the guild to help diagnose issues
    const botMember = state.guild.members.me;
    if (botMember) {
      const botPermissions = botMember.permissions.toArray();
      logger.debug(`Bot permissions in guild: ${botPermissions.join(', ')}`);

      // Check if bot has the minimum required permissions
      const requiredPermissions = ['ViewChannel', 'ManageChannels', 'Connect'];

      const missingPermissions = requiredPermissions.filter(perm => !botPermissions.includes(perm));
      if (missingPermissions.length > 0) {
        logger.warn(`Bot is missing essential permissions: ${missingPermissions.join(', ')}`);
      }
    }

    // Direct fast channel creation for single users
    // Skip queue for better performance unless queue has pending items
    if (channelCreateQueue.length === 0) {
      logger.debug(`Fast-tracking channel creation for ${userName} (bypassing queue)`);
      try {
        await createTemporaryChannel(state, client, categoryId);
      } catch (error) {
        // More detailed error handling for common permission issues
        if (error.code === 50013) {
          // Missing Permissions
          logger.error(`Missing permissions to create channel for ${userName}`);
          logger.debug(`Trying to notify user ${userName} via DM about permission issues`);

          try {
            // Try to move user back to the join channel
            const guildSettings = dataManager.loadGuildSettings();
            const guildConfig = guildSettings[state.guild.id] || {};
            const joinChannelId = guildConfig.joinChannelId;

            if (joinChannelId) {
              const joinChannel = state.guild.channels.cache.get(joinChannelId);
              if (joinChannel) {
                await member.voice.setChannel(joinChannel).catch(() => {});
              }
            }
          } catch {
            // Ignore errors when trying to move users back
          }
        } else {
          logger.error(`Fast-track channel creation failed with error: ${error.message}`);
        }

        // If fast track fails, fall back to queue
        queueChannelCreation(state, client, categoryId);
      }
    } else {
      // Multiple channel creations in progress, use the queue system
      logger.debug(
        `Queueing channel creation for user ${userName} (${channelCreateQueue.length} in queue)`
      );
      queueChannelCreation(state, client, categoryId);
    }
  } catch (error) {
    handleError('handleUserJoiningChannel', error);
    // Make sure to remove the in-progress flags
    if (state.member && state.member.id) {
      activeCreations.delete(state.member.id);
      channelCreationsInProgress.delete(state.member.id);
    }
  }
}

/**
 * Enhanced error handler that provides detailed permission diagnostics
 * @param {string} action - The action being performed
 * @param {Error} error - The error object
 * @param {Guild} guild - The guild where the error occurred
 * @param {string} channelId - ID of the channel (if applicable)
 * @param {string} userId - ID of the user (if applicable)
 */
function handlePermissionError(action, error, guild, channelId = null, userId = null) {
  logger.error(`[PERMISSION ERROR] Failed to ${action}: ${error.message}`);

  try {
    // Get bot's permissions
    const bot = guild.members.me;
    if (!bot) {
      logger.error(`Cannot check permissions: Bot not found in guild ${guild.id}`);
      return;
    }

    // Bot role information
    const botRoles = bot.roles.cache.map(r => ({
      name: r.name,
      id: r.id,
      position: r.position,
      permissions: r.permissions.toArray().join(','),
    }));

    // Get guild roles for reference
    const guildRoles = guild.roles.cache.map(r => ({
      name: r.name,
      id: r.id,
      position: r.position,
    }));

    // Sort roles by position
    guildRoles.sort((a, b) => b.position - a.position);

    // Channel-specific permissions
    let channelPerms = null;
    if (channelId) {
      const channel = guild.channels.cache.get(channelId);
      if (channel) {
        channelPerms = bot.permissionsIn(channel).toArray();
      }
    }

    // User information
    let userInfo = null;
    if (userId) {
      const member = guild.members.cache.get(userId);
      if (member) {
        userInfo = {
          name: member.user.username,
          id: member.id,
          highestRole: member.roles.highest.name,
        };
      }
    }

    // Log detailed permission diagnostic information
    logger.error(`
========== PERMISSION DIAGNOSTIC ==========
Action: ${action}
Error: ${error.message}
Guild: ${guild.name} (${guild.id})
Channel: ${channelId || 'N/A'}
User: ${userId || 'N/A'}

Bot Information:
- Name: ${bot.user.username}
- ID: ${bot.id}
- Highest Role: ${bot.roles.highest.name}
- Guild Permissions: ${bot.permissions.toArray().join(', ')}
${channelPerms ? `- Channel Permissions: ${channelPerms.join(', ')}` : ''}

Role Hierarchy (top 5 roles):
${guildRoles
  .slice(0, 5)
  .map(r => `- ${r.name} (${r.position})`)
  .join('\n')}

Bot Roles:
${botRoles.map(r => `- ${r.name} (${r.position})`).join('\n')}

${
  userInfo
    ? `User Information:
- Name: ${userInfo.name}
- ID: ${userInfo.id}
- Highest Role: ${userInfo.highestRole}`
    : ''
}
===========================================
    `);

    // Try to fix the permission issue for this guild
    fixPermissionIssue(guild, action, error);
  } catch (diagError) {
    logger.error(`Error in permission diagnostics: ${diagError.message}`);
  }
}

/**
 * Attempt to fix common permission issues automatically
 * @param {Guild} guild - The guild to fix
 * @param {string} action - The action that failed
 * @param {Error} error - The error that occurred
 */
function fixPermissionIssue(guild, action, error) {
  try {
    const bot = guild.members.me;
    if (!bot) return;

    // Get the bot's highest role

    // Load guild settings
    const guildSettings = dataManager.loadGuildSettings();
    const guildConfig = guildSettings[guild.id] || {};

    // Get the temp category
    if (guildConfig.tempCategoryId) {
      const category = guild.channels.cache.get(guildConfig.tempCategoryId);
      if (category) {
        logger.info(`Attempting to fix permissions for category ${category.id}`);

        // Try to set minimal permissions for the bot in the category
        category.permissionOverwrites
          .edit(bot.id, {
            ViewChannel: true,
            Connect: true,
            ManageChannels: true,
          })
          .catch(e => {
            logger.warn(`Could not fix category permissions: ${e.message}`);
          });
      }
    }

    // If this is a channel creation issue, try to update parent category permissions
    if (action.includes('createTemporaryChannel') || action.includes('create')) {
      logger.info(`Fixing permissions for channel creation in guild ${guild.id}`);

      // If this is a Missing Permissions error, log details for server admins
      if (error.code === 50013) {
        logger.warn(`
=== ADMIN INSTRUCTIONS ===
The bot is missing permissions needed to create voice channels.
Please ensure the bot has these permissions:
1. View Channels
2. Manage Channels
3. Connect
4. Move Members
5. Manage Roles

You can fix this by going to:
Server Settings > Roles > [Bot Role] > Enable these permissions
======================
        `);
      }
    }
  } catch (e) {
    logger.error(`Error in fixPermissionIssue: ${e.message}`);
  }
}

/**
 * Creates a temporary voice channel for a user
 * This is called by the channel queue processor to control API rate limits
 *
 * @param {VoiceState} state - The voice state update
 * @param {Client} client - The Discord client
 * @param {string} categoryId - The category ID for the new channel
 * @returns {Promise<Channel|null>} The created channel or null if creation failed
 */
async function createTemporaryChannel(state: VoiceState, client, categoryId) {
  if (!state || !state.member) {
    logger.error('Cannot create channel: state or member is null/undefined');
    return null;
  }

  const member = state.member;
  const guild = state.guild;
  const userId = member.id;
  const userName = member.nickname || member.user.username;

  try {
    logger.debug(`Creating temporary channel for ${userName} (ID: ${userId})`);

    // Try to get user settings from MongoDB first for the most up-to-date settings
    let userSettings;
    try {
      // Load this specific user's settings directly from MongoDB
      userSettings = await dataManager.getUserSettings(userId);

      if (userSettings) {
        logger.info(`Found settings in MongoDB for user ${userName}`);

        // Update client cache with the latest settings
        client.userSettings.set(userId, userSettings);
      } else {
        // Fall back to client cache if not found in MongoDB
        userSettings = client.userSettings.get(userId);
        logger.debug(`No settings in MongoDB, using cached settings for user ${userName}`);
      }
    } catch (error) {
      // Fall back to client cache if MongoDB access fails
      logger.error(`Error getting user settings from MongoDB: ${error.message}`);
      userSettings = client.userSettings.get(userId);
    }

    // Log the settings we've found
    if (userSettings) {
      logger.info(`Found saved settings for ${userName}`);
      logger.debug(
        `User settings: name=${userSettings.name || 'default'}, isPrivate=${userSettings.isPrivate || false}, hasWaitingRoom=${userSettings.hasWaitingRoom || false}, trustedUsers=${userSettings.trustedUsers?.length || 0}, blockedUsers=${userSettings.blockedUsers?.length || 0}`
      );
    } else {
      logger.info(`No saved settings found for ${userName}, using defaults`);
    }

    // Get the category
    const category = guild.channels.cache.get(categoryId);
    if (!category) {
      logger.error(`Category ${categoryId} not found`);
      return null;
    }

    logger.debug(`Creating channel for ${userName} with saved settings`);

    try {
      // Use a more reliable approach - create without parent first, then set parent
      // This avoids common permission issues with direct parent creation
      const channelName = userSettings?.name || `${userName}'s channel`;
      const channel = await guild.channels.create({
        name: channelName,
        type: ChannelType.GuildVoice,
        userLimit: userSettings?.userLimit || 0,
        bitrate: userSettings?.bitrate || 64000,
        rtcRegion: userSettings?.rtcRegion || null,
        parent: category.id,
      });

      logger.info(`Successfully created channel ${channel.name} (${channel.id}) for ${userName}`);

      // Track the channel immediately
      client.tempChannels.set(channel.id, userId);

      // Move the user to the channel
      await member.voice.setChannel(channel).catch(error => {
        logger.warn(`Could not move user to new channel: ${error.message}`);
      });

      // Send welcome message with purge button
      setTimeout(async () => {
        await sendWelcomeMessage(channel, userId, client);
      }, 1000); // Small delay to ensure the user has been moved

      // Save the updated tempChannels Map to disk
      dataManager.saveTempChannels(client.tempChannels);

      // Set parent after creation
      // await channel.setParent(category.id, { lockPermissions: false }).catch(error => {
      //   logger.warn(`Could not set parent category: ${error.message}`);
      // });

      // Apply permissions from saved settings
      try {
        // Add minimal bot permissions
        await channel.permissionOverwrites.edit(guild.members.me.id, {
          ViewChannel: true,
          Connect: true,
          ManageChannels: true,
          ManageRoles: null,
          MuteMembers: null,
          DeafenMembers: null,
          MoveMembers: null,
        });

        // Add owner permissions
        await channel.permissionOverwrites.edit(member.id, {
          ViewChannel: true,
          Connect: true,
          Speak: true,
          // Stream: true,
          // MuteMembers: null,
          // DeafenMembers: null,
          // MoveMembers: null,
        });

        // Set privacy if needed
        if (userSettings?.isPrivate) {
          await channel.permissionOverwrites.edit(guild.roles.everyone.id, {
            ViewChannel: null,
          });
          logger.debug(`Applied privacy setting to channel`);
        }

        // Set waiting room if needed
        if (userSettings?.hasWaitingRoom) {
          await channel.permissionOverwrites.edit(guild.roles.everyone.id, {
            Connect: null,
          });
          logger.debug(`Applied waiting room setting to channel`);
        }

        // First, try to get all guild members in one batch to avoid rate limits
        const guildMemberIds = new Set<string>();
        try {
          const members = await guild.members.fetch({ time: 5000 }).catch(() => null);
          if (members) {
            members.forEach((member: any) => guildMemberIds.add(member.id));
            logger.debug(
              `Fetched ${guildMemberIds.size} members from guild ${guild.id} for permission check`
            );
          }
        } catch (error: any) {
          logger.debug(
            `Could not fetch all guild members: ${error.message}. Will apply permissions directly.`
          );
        }

        // Add trusted users
        if (userSettings?.trustedUsers && userSettings.trustedUsers.length > 0) {
          let appliedCount = 0;

          for (const userId of userSettings.trustedUsers) {
            try {
              // Apply permissions directly without checking if user is in guild
              // Discord will handle this gracefully and the permissions will apply when user joins
              await channel.permissionOverwrites.edit(userId, {
                ViewChannel: true,
                Connect: true,
              });
              appliedCount++;

              // Just log differently if we know the user isn't in the guild
              if (guildMemberIds.size > 0 && !guildMemberIds.has(userId)) {
                logger.debug(
                  `Applied trusted permissions for user ${userId} who is not currently in guild ${guild.id}`
                );
              }
            } catch (error) {
              logger.warn(
                `Failed to apply trusted permission for user ${userId}: ${error.message}`
              );
              // Continue with other users even if this one fails
            }
          }
          logger.debug(
            `Applied ${appliedCount}/${userSettings.trustedUsers.length} trusted users to channel`
          );
        }

        // Add blocked users
        if (userSettings?.blockedUsers && userSettings.blockedUsers.length > 0) {
          let appliedCount = 0;

          // We already fetched guild members above, no need to do it again

          for (const userId of userSettings.blockedUsers) {
            try {
              // Apply permissions directly without checking if user is in guild
              // Discord will handle this gracefully and the permissions will apply when user joins
              await channel.permissionOverwrites.edit(userId, {
                ViewChannel: false,
                Connect: false,
              });
              appliedCount++;

              // Just log differently if we know the user isn't in the guild
              if (guildMemberIds.size > 0 && !guildMemberIds.has(userId)) {
                logger.debug(
                  `Applied blocked permissions for user ${userId} who is not currently in guild ${guild.id}`
                );
              }
            } catch (error: any) {
              logger.warn(
                `Failed to apply blocked permission for user ${userId}: ${error.message}`
              );
              // Continue with other users even if this one fails
            }
          }
          logger.debug(
            `Applied ${appliedCount}/${userSettings.blockedUsers.length} blocked users to channel`
          );
        }
      } catch (permError) {
        logger.error(`Error setting permissions: ${permError.message}`);
      }

      // Cleanup - remove user from creation tracking sets
      channelCreationsInProgress.delete(userId);
      activeCreations.delete(userId);
      logger.debug(`Removed ${userName} from creation tracking after successful channel creation`);

      return channel;
    } catch (error) {
      logger.warn(`Failed to create channel: ${error.message}`);

      try {
        // Create channel with minimal settings as fallback
        const fallbackChannel = await guild.channels.create({
          name: userSettings?.name || `${userName}'s channel`,
          type: ChannelType.GuildVoice,
          userLimit: userSettings?.userLimit || 0,
          bitrate: userSettings?.bitrate || 64000,
          rtcRegion: userSettings?.rtcRegion || null,
        });

        logger.info(`Created fallback channel ${fallbackChannel.name} (${fallbackChannel.id})`);

        // Track the channel
        client.tempChannels.set(fallbackChannel.id, userId);

        // Move the user
        await member.voice.setChannel(fallbackChannel).catch(moveError => {
          logger.warn(`Could not move user to fallback channel: ${moveError.message}`);
        });

        // Send welcome message with purge button for fallback channel
        setTimeout(async () => {
          await sendWelcomeMessage(fallbackChannel, userId, client);
        }, 1000); // Small delay to ensure the user has been moved

        // Save the updated tempChannels Map to disk
        dataManager.saveTempChannels(client.tempChannels);

        // Set parent after creation
        // await fallbackChannel
        //   .setParent(category.id, { lockPermissions: false })
        //   .catch(() => {});

        // Apply minimal permissions
        try {
          await fallbackChannel.permissionOverwrites
            .edit(guild.members.me.id, {
              ViewChannel: true,
              Connect: true,
              ManageChannels: true,
            })
            .catch(() => {});

          // Add minimal owner permissions
          await fallbackChannel.permissionOverwrites
            .edit(member.id, {
              ViewChannel: true,
              Connect: true,
              Speak: true,
            })
            .catch(() => {});

          // First, try to get all guild members in one batch to avoid rate limits
          const fallbackGuildMemberIds = new Set<string>();
          try {
            const members = await guild.members.fetch({ time: 5000 }).catch(() => null);
            if (members) {
              members.forEach((member: any) => fallbackGuildMemberIds.add(member.id));
              logger.debug(
                `Fetched ${fallbackGuildMemberIds.size} members from guild ${guild.id} for fallback channel permission check`
              );
            }
          } catch (error: any) {
            logger.debug(
              `Could not fetch all guild members for fallback channel: ${error.message}. Will apply permissions directly.`
            );
          }

          // Add trusted users with error handling
          if (userSettings?.trustedUsers && userSettings.trustedUsers.length > 0) {
            let appliedCount = 0;
            for (const userId of userSettings.trustedUsers) {
              try {
                // Apply permissions directly without checking if user is in guild
                await fallbackChannel.permissionOverwrites
                  .edit(userId, {
                    ViewChannel: true,
                    Connect: true,
                  })
                  .catch(() => {});
                appliedCount++;

                // Just log differently if we know the user isn't in the guild
                if (fallbackGuildMemberIds.size > 0 && !fallbackGuildMemberIds.has(userId)) {
                  logger.debug(
                    `Applied trusted permissions for user ${userId} who is not currently in guild ${guild.id} (fallback channel)`
                  );
                }
              } catch (error: any) {
                logger.warn(
                  `Failed to apply trusted permission for user ${userId} in fallback channel: ${error.message}`
                );
                // Continue with other users even if this one fails
              }
            }
            logger.debug(
              `Applied ${appliedCount}/${userSettings.trustedUsers.length} trusted users to fallback channel`
            );
          }

          // Add blocked users with error handling
          if (userSettings?.blockedUsers && userSettings.blockedUsers.length > 0) {
            let appliedCount = 0;
            for (const userId of userSettings.blockedUsers) {
              try {
                // Apply permissions directly without checking if user is in guild
                await fallbackChannel.permissionOverwrites
                  .edit(userId, {
                    ViewChannel: false,
                    Connect: false,
                  })
                  .catch(() => {});
                appliedCount++;

                // Just log differently if we know the user isn't in the guild
                if (fallbackGuildMemberIds.size > 0 && !fallbackGuildMemberIds.has(userId)) {
                  logger.debug(
                    `Applied blocked permissions for user ${userId} who is not currently in guild ${guild.id} (fallback channel)`
                  );
                }
              } catch (error: any) {
                logger.warn(
                  `Failed to apply blocked permission for user ${userId} in fallback channel: ${error.message}`
                );
                // Continue with other users even if this one fails
              }
            }
            logger.debug(
              `Applied ${appliedCount}/${userSettings.blockedUsers.length} blocked users to fallback channel`
            );
          }
        } catch (err) {
          logger.error(`Error setting fallback permissions: ${err.message}`);
        }

        return fallbackChannel;
      } catch (fallbackError) {
        logger.error(`Fallback channel creation failed: ${fallbackError.message}`);
        throw fallbackError; // Rethrow to be handled by the outer catch
      }
    }
  } catch (error) {
    logger.error(`Error in createTemporaryChannel: ${error.message}`);
    channelCreationsInProgress.delete(userId);
    activeCreations.delete(userId);
    return null;
  }
}

/**
 * Periodically clean up cooldowns and pending moves
 * This prevents memory leaks from users who disconnect during channel creation
 */
setInterval(() => {
  try {
    const now = Date.now();
    let removedCooldowns = 0;
    let removedPendingMoves = 0;
    let removedCreations = 0;

    // Fast cleanup of cooldowns - optimized for performance
    for (const [userId, cooldownEnd] of userCooldowns.entries()) {
      if (now > cooldownEnd) {
        userCooldowns.delete(userId);
        removedCooldowns++;
      }
    }

    // Fast cleanup of pending moves
    for (const [userId, moveData] of pendingMoves.entries()) {
      if (now - moveData.timestamp > 3000) {
        // Reduced from 5000ms to 3000ms
        pendingMoves.delete(userId);
        removedPendingMoves++;
      }
    }

    // Fast cleanup of stuck creations
    for (const userId of channelCreationsInProgress) {
      channelCreationsInProgress.delete(userId);
      activeCreations.delete(userId);
      removedCreations++;
    }

    // Only log if something was cleaned up
    if (removedCooldowns > 0 || removedPendingMoves > 0 || removedCreations > 0) {
      logger.debug(
        `Cleanup: Removed ${removedCooldowns} cooldowns, ${removedPendingMoves} pending moves, ${removedCreations} stuck creations`
      );
    }
  } catch (error) {
    logger.error(`Error in cleanup interval: ${error.message}`);
  }
}, 2500); // Run every 2.5 seconds - reduced from 5 seconds

/**
 * Check for empty channels in MyVC Category every 20 seconds and delete them immediately
 * This helps clean up orphaned channels when users switch between guilds
 */
setInterval(async () => {
  try {
    if (!global.client || !global.client.isReady()) return;

    const client = global.client;
    let channelsDeleted = 0;

    // Load guild settings to get tempCategoryIds
    const guildSettings = await dataManager.loadGuildSettings();

    // Process each guild the bot is in
    for (const guild of client.guilds.cache.values()) {
      try {
        // Skip guilds that aren't set up
        const guildConfig = guildSettings[guild.id];
        if (!guildConfig || !guildConfig.tempCategoryId) continue;

        const categoryId = guildConfig.tempCategoryId;
        const joinChannelId = guildConfig.joinChannelId;

        // Get the category
        const category = await guild.channels.fetch(categoryId).catch(() => null);
        if (!category) continue;

        // Check all channels in the category
        for (const [channelId, channel] of guild.channels.cache.entries()) {
          // Skip if not in the MyVC category
          if (channel.parentId !== categoryId) continue;

          // Skip the join channel
          if (channelId === joinChannelId) continue;

          // Skip channels with "join" in the name (likely join channels)
          if (channel.name.toLowerCase().includes('join')) continue;

          // If the channel is empty, delete it immediately
          if (channel.type === ChannelType.GuildVoice && channel.members.size === 0) {
            logger.info(
              `Category cleanup: Found empty channel ${channel.name} (${channelId}), deleting immediately`
            );

            // Remove from tracking
            client.tempChannels.delete(channelId);

            // Delete the channel
            try {
              await channel.delete(`Automatic category cleanup: Channel empty`);
              channelsDeleted++;
              logger.info(`Category cleanup: Successfully deleted empty channel ${channelId}`);
            } catch (error) {
              logger.error(
                `Category cleanup: Failed to delete channel ${channelId}: ${error.message}`
              );
            }
          }
        }
      } catch (error) {
        logger.error(`Error cleaning up guild ${guild.id}: ${error.message}`);
      }
    }

    if (channelsDeleted > 0) {
      logger.info(`Category cleanup complete: Deleted ${channelsDeleted} empty channels`);

      // Save the updated tempChannels map
      dataManager.saveTempChannels(client.tempChannels).catch(err => {
        logger.error(`Failed to save temp channels during category cleanup: ${err.message}`);
      });
    }
  } catch (error) {
    logger.error(`Error in category cleanup interval: ${error.message}`);
  }
}, 20000); // Run every 20 seconds

/**
 * Enhanced general error handler to properly direct errors
 */
function handleError(context: string, error: DiscordError): void {
  try {
    // Log the basic error
    logger.error(`Error in ${context}: ${error.message}`);

    // Check if this is a permission error (Discord API Error code 50013)
    if (error.code === 50013) {
      // Missing Permissions
      // If we have guild information, call the detailed permission handler
      if (error.guild || error.guildId) {
        const guild = error.guild;
        const channelId = error.channelId;
        const userId = error.userId;
        handlePermissionError(context, error, guild, channelId, userId);
        return;
      }
    }

    // Log stack trace for non-permission errors
    if (error.stack) {
      logger.debug(
        `Stack trace for ${context} error: ${error.stack.split('\n').slice(0, 3).join('\n')}`
      );
    }
  } catch (handlerError) {
    // If our error handler has an error, log it but don't cause further issues
    logger.error(`Error in error handler: ${handlerError.message}`);
  }
}

// /**
//  * Handle permission errors with detailed information
//  * @param {string} context - The context where the error occurred
//  * @param {Error} error - The error object
//  * @param {Guild} guild - The guild where the error occurred
//  * @param {string} channelId - The channel ID where the error occurred
//  * @param {string} userId - The user ID related to the error
//  */
// function handlePermissionError(context, error, guild, channelId, userId) {
//   // Implementation for permission error handling
//   logger.error(`Permission error in ${context}: ${error.message}`);
// }
