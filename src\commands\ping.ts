import {
  <PERSON><PERSON>,
  <PERSON>bed<PERSON><PERSON><PERSON>,
  SlashCommandBuilder,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  ComponentType,
  ButtonInteraction,
  ChatInputCommandInteraction,
} from 'discord.js';
import logger from '../utils/logger';
import { EMOJI } from '../constants/emoji';

export const data = new SlashCommandBuilder()
  .setName('ping')
  .setDescription('Check the Discord API connection with style');

export const execute = async (interaction: ChatInputCommandInteraction, client: Client) => {
  try {
    // Defer reply to allow for animation (ephemeral = true)
    await interaction.deferReply({ ephemeral: true });

    // Get initial API ping
    const initialPing = Math.round(client.ws.ping);

    // Create a function to get a visual representation of the ping
    const getPingBar = (ping: number): string => {
      // Define ping quality thresholds
      const excellent = 50;
      const good = 100;
      const okay = 200;
      const poor = 300;

      // Calculate how many bars to fill based on ping
      const totalBars = 10;
      let filledBars = 0;

      if (ping <= excellent) {
        filledBars = 10;
      } else if (ping <= good) {
        filledBars = 8;
      } else if (ping <= okay) {
        filledBars = 6;
      } else if (ping <= poor) {
        filledBars = 4;
      } else {
        filledBars = 2;
      }

      // Create the visual bar
      const fullBar = '█';
      const emptyBar = '░';

      return fullBar.repeat(filledBars) + emptyBar.repeat(totalBars - filledBars);
    };

    // Get ping quality description
    const getPingQuality = (ping: number): { emoji: string; text: string; color: number } => {
      if (ping < 50) {
        return {
          emoji: '🚀',
          text: 'Supersonic',
          color: 0x3498db, // Blue
        };
      } else if (ping < 100) {
        return {
          emoji: '⚡',
          text: 'Excellent',
          color: 0x2ecc71, // Green
        };
      } else if (ping < 200) {
        return {
          emoji: '✨',
          text: 'Good',
          color: 0xf1c40f, // Yellow
        };
      } else if (ping < 300) {
        return {
          emoji: '🔶',
          text: 'Okay',
          color: 0xe67e22, // Orange
        };
      } else {
        return {
          emoji: '🔴',
          text: 'Slow',
          color: 0xe74c3c, // Red
        };
      }
    };

    // Get fun fact about ping based on its value
    const getPingFact = (ping: number): string => {
      const facts = [
        `Did you know? Light travels around Earth 7.5 times in one second. Your ping just traveled to Discord and back in ${ping}ms!`,
        `Fun fact: A hummingbird's wings flap about 50 times in ${ping}ms!`,
        `Neuron response time in humans is about 100ms. Your Discord connection is ${ping < 100 ? 'faster' : 'slower'} than your neurons!`,
        `In the time of your ${ping}ms ping, light traveled approximately ${Math.round(ping * 300)} kilometers!`,
        `The average human reaction time is 250ms. Your Discord connection is ${ping < 250 ? 'faster' : 'slower'} than that!`,
      ];

      // Select a fact based on ping value to make it somewhat relevant
      const factIndex = Math.min(Math.floor(ping / 100), facts.length - 1);
      return facts[factIndex];
    };

    // Get ping quality info
    const qualityInfo = getPingQuality(initialPing);

    // Create the ping embed
    const pingEmbed = new EmbedBuilder()
      .setTitle(`${qualityInfo.emoji} Discord Ping: ${initialPing}ms`)
      .setDescription(
        `
\`\`\`
Connection Quality: ${qualityInfo.text}
Signal Strength: [${getPingBar(initialPing)}] ${initialPing}ms
\`\`\`

${getPingFact(initialPing)}
      `
      )
      .setColor(qualityInfo.color)
      .setFooter({
        text: `Requested by ${interaction.user.tag} • Discord API Latency`,
        iconURL: interaction.user.displayAvatarURL(),
      })
      .setTimestamp();

    // Create a refresh button
    const row = new ActionRowBuilder<ButtonBuilder>().addComponents(
      new ButtonBuilder()
        .setCustomId('refresh_ping')
        .setLabel('Refresh')
        .setEmoji('🔄')
        .setStyle(ButtonStyle.Secondary)
    );

    // Send the initial response
    const response = await interaction.editReply({
      embeds: [pingEmbed],
      components: [row],
    });

    // Create a collector for button interactions
    const collector = response.createMessageComponentCollector({
      componentType: ComponentType.Button,
      time: 60000, // Collect for 1 minute
    });

    // Handle button clicks
    collector.on('collect', async (i: ButtonInteraction) => {
      if (i.user.id === interaction.user.id) {
        // Get a new ping measurement
        const newPing = Math.round(client.ws.ping);
        const newQualityInfo = getPingQuality(newPing);

        // Create updated embed
        const updatedEmbed = new EmbedBuilder()
          .setTitle(`${newQualityInfo.emoji} Discord Ping: ${newPing}ms`)
          .setDescription(
            `
\`\`\`
Connection Quality: ${newQualityInfo.text}
Signal Strength: [${getPingBar(newPing)}] ${newPing}ms
\`\`\`

${getPingFact(newPing)}
          `
          )
          .setColor(newQualityInfo.color)
          .setFooter({
            text: `Requested by ${interaction.user.tag} • Refreshed • Discord API Latency`,
            iconURL: interaction.user.displayAvatarURL(),
          })
          .setTimestamp();

        // Update the message
        await i.update({ embeds: [updatedEmbed], components: [row] });
      } else {
        // If someone else clicks the button
        await i.reply({
          content: `This ping command was used by ${interaction.user.tag}. Please run your own /ping command.`,
          ephemeral: true,
        });
      }
    });

    // When the collector ends, remove the button
    collector.on('end', async () => {
      // Get the final ping for the disabled state
      const finalPing = Math.round(client.ws.ping);
      const finalQualityInfo = getPingQuality(finalPing);

      // Create final embed
      const finalEmbed = new EmbedBuilder()
        .setTitle(`${finalQualityInfo.emoji} Discord Ping: ${finalPing}ms`)
        .setDescription(
          `
\`\`\`
Connection Quality: ${finalQualityInfo.text}
Signal Strength: [${getPingBar(finalPing)}] ${finalPing}ms
\`\`\`

${getPingFact(finalPing)}
        `
        )
        .setColor(finalQualityInfo.color)
        .setFooter({
          text: `Requested by ${interaction.user.tag} • Refresh expired • Discord API Latency`,
          iconURL: interaction.user.displayAvatarURL(),
        })
        .setTimestamp();

      // Create a disabled button row
      const disabledRow = new ActionRowBuilder<ButtonBuilder>().addComponents(
        new ButtonBuilder()
          .setCustomId('refresh_ping_disabled')
          .setLabel('Refresh')
          .setEmoji('🔄')
          .setStyle(ButtonStyle.Secondary)
          .setDisabled(true)
      );

      // Update the message with disabled button
      await interaction
        .editReply({
          embeds: [finalEmbed],
          components: [disabledRow],
        })
        .catch(() => {
          // Ignore errors if the message was deleted
        });
    });

    // Log the ping
    logger.info(`Ping command used by ${interaction.user.tag} - API Latency: ${initialPing}ms`);
  } catch (error) {
    logger.error(`Error in ping command: ${error.message}`);

    // Provide error feedback to the user
    if (!interaction.replied && !interaction.deferred) {
      await interaction.reply({
        content: `${EMOJI[client.user.id].CROSS} An error occurred while checking the ping.`,
        ephemeral: true,
      });
    } else {
      await interaction.editReply({
        content: `${EMOJI[client.user.id].CROSS} An error occurred while checking the ping.`,
        components: [],
      });
    }
  }
};
