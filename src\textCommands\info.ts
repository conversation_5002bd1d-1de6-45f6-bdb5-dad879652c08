/**
 * Info Command
 * Displays information about the bot and its developers
 */
import { EmbedBuilder } from 'discord.js';
import { Message, Client, Collection } from 'discord.js';
import { formatUptime } from '../utils/helpers';
import { DEVELOPER_IDS } from '../constants/devs';
import logger from '../utils/logger';

// Augment Client interface for custom properties
declare module 'discord.js' {
  interface Client {
    // Assuming tempChannels is a Collection or similar with a size property
    tempChannels: Collection<any, any> | Map<any, any> | { size: number }; // Adjust type as needed
  }
}

export const name = 'info';
export const description = 'Displays information about the bot and its developers';
export const devOnly = false;
export const adminOnly = false;

export const execute = async (message: Message, args: string[], client: Client) => {
  try {
    // Calculate uptime
    const uptime = formatUptime(client.uptime);

    // Create the info embed
    const infoEmbed = new EmbedBuilder()
      .setColor(0x5865f2) // Discord Blurple
      .setTitle('Bot Information')
      .setDescription('MyVC Discord Bot - Creates customizable temporary voice channels')
      .setThumbnail(client.user.displayAvatarURL())
      .addFields(
        {
          name: '📊 Stats',
          value: [
            `📡 **Servers**: ${client.guilds.cache.size}`,
            `🔊 **Temp Channels**: ${client.tempChannels.size}`,
            `⏱️ **Uptime**: ${uptime}`,
            `🏓 **Ping**: ${Math.round(client.ws.ping)}ms`,
          ].join('\n'),
          inline: true,
        },
        {
          name: '👨‍💻 Developers',
          value: await fetchDeveloperInfo(client),
          inline: true,
        }
      )
      .setFooter({
        text: `Requested by ${message.author.tag}`,
        iconURL: message.author.displayAvatarURL(),
      })
      .setTimestamp();

    // Send the embed
    await message.reply({ embeds: [infoEmbed], allowedMentions: { repliedUser: false } });
  } catch (error) {
    logger.error('Error in info command:', error);
    message
      .reply({
        content: 'An error occurred while getting bot info.',
        allowedMentions: { repliedUser: false },
      })
      .catch(err => logger.error('Error sending error message:', err));
  }
};

/**
 * Fetch developer info for the embed
 * @param {Client} client - Discord.js client
 * @returns {string} - Formatted developer info
 */
async function fetchDeveloperInfo(client: Client): Promise<string> {
  try {
    const developerInfo: string[] = [];

    for (const devId of DEVELOPER_IDS) {
      try {
        // Try to fetch user from cache or API
        const user = await client.users.fetch(devId, { force: false });
        developerInfo.push(`<@${devId}> (${user.tag})`);
      } catch {
        // Failed to fetch user, just use ID
        developerInfo.push(`<@${devId}>`);
      }
    }

    return developerInfo.join('\n');
  } catch (error) {
    logger.error('Error fetching developer info:', error);
    return DEVELOPER_IDS.map(id => `<@${id}>`).join('\n');
  }
}
