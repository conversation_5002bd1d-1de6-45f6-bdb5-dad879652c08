/**
 * Info Command
 * Displays information about the bot in Carl-bot style
 */
import { EmbedBuilder } from 'discord.js';
import { Message } from 'discord.js';
import { formatUptime, formatBytes } from '../utils/helpers';
import logger from '../utils/logger';
import { ExtendedClient } from '../types';

export const name = 'info';
export const description = 'Displays information about the bot';
export const devOnly = false;
export const adminOnly = false;

export const execute = async (message: Message, _args: string[], client: ExtendedClient) => {
  try {
    // Get memory usage
    const memoryUsage = process.memoryUsage();
    const totalMemory = memoryUsage.rss;

    // Calculate CPU usage (simplified)
    const cpuUsage = process.cpuUsage();
    const cpuPercent = ((cpuUsage.user + cpuUsage.system) / 1000000 / (process.uptime() * 1000)) * 100;

    // Get total members across all guilds
    const totalMembers = client.guilds.cache.reduce((acc, guild) => acc + guild.memberCount, 0);

    // Get total channels across all guilds
    const totalChannels = client.guilds.cache.reduce((acc, guild) => acc + guild.channels.cache.size, 0);

    // Get total voice channels
    const totalVoiceChannels = client.guilds.cache.reduce((acc, guild) =>
      acc + guild.channels.cache.filter(c => c.type === 2).size, 0
    );

    // Get bot statistics
    const stats = global.getStats ? global.getStats() : {
      totalMessagesProcessed: 0,
      totalCommandsUsed: 0,
      startTime: Date.now(),
    };

    // Calculate uptime
    const uptime = formatUptime(client.uptime);

    // Calculate messages per second
    const uptimeSeconds = (Date.now() - stats.startTime) / 1000;
    const messagesPerSecond = uptimeSeconds > 0 ? (stats.totalMessagesProcessed / uptimeSeconds).toFixed(1) : '0.0';

    // Create the info embed in Carl-bot style
    const infoEmbed = new EmbedBuilder()
      .setColor(0x36393f) // Dark gray like Carl-bot
      .setAuthor({
        name: `${client.user.username}`,
        iconURL: client.user.displayAvatarURL(),
      })
      .setDescription('**About:**\n[Dashboard](https://example.com/dashboard)\n[Invite link](https://discord.com/oauth2/authorize?client_id=' + client.user.id + '&permissions=8&scope=bot)\n[Commands](https://example.com/commands)\n[Bot support server](https://discord.gg/example)\n[Discord bot list Vote](https://example.com/vote)\n[Patreon link](https://patreon.com/example)\n\nMyVC does what the most popular bots do but does it better, faster, and without the meme commands that spam and annoy you. MyVC has been used to reduce the channels needed on a server from 5 to 4 just... to just 1.')
      .addFields(
        {
          name: 'Members',
          value: `${totalMembers.toLocaleString()} total\n${client.guilds.cache.size.toLocaleString()} unique`,
          inline: true,
        },
        {
          name: 'Channels',
          value: `${totalChannels.toLocaleString()} total\n${totalVoiceChannels.toLocaleString()} voice`,
          inline: true,
        },
        {
          name: 'Process',
          value: `${formatBytes(totalMemory)}\n${cpuPercent.toFixed(1)}% CPU`,
          inline: true,
        },
        {
          name: 'Servers',
          value: client.guilds.cache.size.toString(),
          inline: true,
        },
        {
          name: 'Messages seen',
          value: `${stats.totalMessagesProcessed.toLocaleString()} messages (${messagesPerSecond}/s)`,
          inline: true,
        },
        {
          name: 'Uptime',
          value: uptime,
          inline: true,
        }
      )
      .setFooter({
        text: `Made with ❤️ with Discord.js`,
        iconURL: 'https://cdn.discordapp.com/emojis/596577034537402378.png?size=64',
      });

    // Send the embed
    await message.reply({ embeds: [infoEmbed], allowedMentions: { repliedUser: false } });
  } catch (error) {
    logger.error('Error in info command:', error);
    message
      .reply({
        content: 'An error occurred while getting bot info.',
        allowedMentions: { repliedUser: false },
      })
      .catch(err => logger.error('Error sending error message:', err));
  }
};


