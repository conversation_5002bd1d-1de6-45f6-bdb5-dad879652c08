#!/usr/bin/env node
/**
 * Guild Logs Viewer
 * 
 * This script displays the guild join/leave logs in a readable format.
 * It can filter by event type, guild ID, or date range.
 */

import * as fs from 'fs';
import * as path from 'path';
import * as readline from 'readline';

// Define interfaces for the log events
interface BaseGuildEvent {
  event: 'JOIN' | 'LEAVE';
  timestamp: string;
  guildId: string;
  guildName: string;
  ownerID: string;
}

interface GuildJoinEvent extends BaseGuildEvent {
  event: 'JOIN';
  memberCount: number;
  region: string;
  features: string[];
  channels: {
    total: number;
    text: number;
    voice: number;
    categories: number;
  };
  roles: number;
  createdAt: string;
}

interface GuildLeaveEvent extends BaseGuildEvent {
  event: 'LEAVE';
  memberCount?: number;
  reason: string;
  joinedAt?: string;
  leftAt: string;
}

type GuildEvent = GuildJoinEvent | GuildLeaveEvent;

// Log directory for guild events
const GUILD_LOG_DIR: string = path.join(process.cwd(), 'data', 'logs');

// Check if the directory exists
if (!fs.existsSync(GUILD_LOG_DIR)) {
  console.error(`Guild logs directory not found: ${GUILD_LOG_DIR}`);
  process.exit(1);
}

// Parse command line arguments
const args: string[] = process.argv.slice(2);
let filterGuildId: string | null = null;
let filterEventType: 'JOIN' | 'LEAVE' | null = null;
let filterMonth: string | null = null;

// Process arguments
for (let i = 0; i < args.length; i++) {
  if (args[i] === '--guild' && i + 1 < args.length) {
    filterGuildId = args[i + 1];
    i++;
  } else if (args[i] === '--event' && i + 1 < args.length) {
    const eventType = args[i + 1].toUpperCase();
    if (eventType === 'JOIN' || eventType === 'LEAVE') {
      filterEventType = eventType;
    } else {
      console.error(`Invalid event type: ${eventType}. Must be JOIN or LEAVE.`);
      process.exit(1);
    }
    i++;
  } else if (args[i] === '--month' && i + 1 < args.length) {
    filterMonth = args[i + 1];
    // Validate month format (YYYY-MM)
    if (!/^\d{4}-\d{2}$/.test(filterMonth)) {
      console.error(`Invalid month format: ${filterMonth}. Must be YYYY-MM.`);
      process.exit(1);
    }
    i++;
  } else if (args[i] === '--help') {
    console.log(`
Guild Logs Viewer

Usage:
  ts-node view-guild-logs.ts [options]

Options:
  --guild <id>     Filter by guild ID
  --event <type>   Filter by event type (JOIN or LEAVE)
  --month <YYYY-MM> Filter by month (e.g., 2023-05)
  --help           Show this help message

Examples:
  ts-node view-guild-logs.ts
  ts-node view-guild-logs.ts --guild 123456789012345678
  ts-node view-guild-logs.ts --event JOIN
  ts-node view-guild-logs.ts --month 2023-05
    `);
    process.exit(0);
  }
}

// Get all log files
const logFiles: string[] = fs.readdirSync(GUILD_LOG_DIR)
  .filter(file => file.startsWith('guild-events-') && file.endsWith('.log'))
  .sort((a, b) => b.localeCompare(a)); // Sort in reverse chronological order

// Filter by month if specified
if (filterMonth) {
  const monthPattern = `guild-events-${filterMonth}`;
  const filteredFiles = logFiles.filter(file => file.includes(monthPattern));
  if (filteredFiles.length === 0) {
    console.error(`No log files found for month: ${filterMonth}`);
    process.exit(1);
  }
  console.log(`Filtering logs for month: ${filterMonth}`);
  logFiles.length = 0;
  logFiles.push(...filteredFiles);
}

if (logFiles.length === 0) {
  console.error('No guild log files found');
  process.exit(1);
}

console.log(`Found ${logFiles.length} log files`);
console.log('Filters:', {
  guildId: filterGuildId || 'None',
  eventType: filterEventType || 'None',
  month: filterMonth || 'None'
});
console.log('\n--- Guild Events Log ---\n');

// Process each log file
async function processLogFiles(): Promise<void> {
  let totalEvents = 0;
  let filteredEvents = 0;

  for (const file of logFiles) {
    const filePath = path.join(GUILD_LOG_DIR, file);
    
    const fileStream = fs.createReadStream(filePath);
    const rl = readline.createInterface({
      input: fileStream,
      crlfDelay: Infinity
    });

    for await (const line of rl) {
      totalEvents++;
      
      try {
        const event = JSON.parse(line) as GuildEvent;
        
        // Apply filters
        if (filterGuildId && event.guildId !== filterGuildId) continue;
        if (filterEventType && event.event !== filterEventType) continue;
        
        filteredEvents++;
        
        // Format the output
        const timestamp = new Date(event.timestamp).toLocaleString();
        const eventType = event.event === 'JOIN' ? '➕ JOIN' : '➖ LEAVE';
        
        console.log(`[${timestamp}] ${eventType} | ${event.guildName} (${event.guildId})`);
        
        if (event.event === 'JOIN') {
          console.log(`  Members: ${event.memberCount} | Owner: ${event.ownerID}`);
          console.log(`  Channels: ${event.channels.total} (${event.channels.text} text, ${event.channels.voice} voice)`);
        } else {
          console.log(`  Reason: ${event.reason}`);
          if (event.joinedAt) {
            const joinedAt = new Date(event.joinedAt).toLocaleString();
            const leftAt = new Date(event.leftAt).toLocaleString();
            const duration = Math.floor((new Date(event.leftAt).getTime() - new Date(event.joinedAt).getTime()) / (1000 * 60 * 60 * 24));
            console.log(`  Duration: ${duration} days (Joined: ${joinedAt}, Left: ${leftAt})`);
          }
        }
        
        console.log(''); // Empty line for readability
      } catch (error) {
        if (error instanceof Error) {
          console.error(`Error parsing log entry: ${error.message}`);
        } else {
          console.error(`Unknown error parsing log entry`);
        }
      }
    }
  }
  
  console.log(`\nDisplayed ${filteredEvents} events out of ${totalEvents} total events`);
}

processLogFiles().catch(error => {
  if (error instanceof Error) {
    console.error('Error processing log files:', error.message);
  } else {
    console.error('Unknown error processing log files');
  }
  process.exit(1);
});
