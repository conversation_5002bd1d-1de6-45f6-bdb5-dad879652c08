import { ModalBuilder, TextInputBuilder, TextInputStyle, ActionRowBuilder } from 'discord.js';
import logger from '../utils/logger';
import { replyOrFollowUpEphemeral, handleInteractionError } from '../utils/interactionUtils';
import { EMOJI } from '../constants/emoji';

export const name = 'name';

// Called when the "Name" button is clicked
// Interaction is NOT deferred here because showModal is used.
// Ownership & cooldown checked in interactionCreate.ts
export const execute = async (interaction, client, userChannel) => {
  // Shows the modal to the user
  try {
    const modal = new ModalBuilder()
      .setCustomId(`name_modal_${userChannel.id}`) // Consistent ID format
      .setTitle('Rename Channel');

    const input = new TextInputBuilder()
      .setCustomId('channel_name') // Consistent field ID
      .setLabel('New Channel Name')
      .setPlaceholder('Enter the new name')
      .setRequired(true)
      .setStyle(TextInputStyle.Short)
      // Empty field instead of pre-filling with current name
      .setMinLength(1)
      .setMaxLength(100); // Discord limit is 100

    const actionRow: any = new ActionRowBuilder().addComponents(input);
    modal.addComponents(actionRow);

    await interaction.showModal(modal);
    // No reply needed here, handled by handleModalSubmit
  } catch (error) {
    logger.error(`Error showing name modal: ${error.message}`);
    await handleInteractionError('name.execute', error, interaction, client);
  }
};

// Called when the modal created above is submitted
export const handleModalSubmit = async (interaction, client, targetChannel) => {
  const modalSource = `name.handleModalSubmit:modal:${interaction.customId}`;
  // Interaction is deferred in interactionCreate.ts before calling this
  // Cooldown & ownership also checked before calling this
  try {
    const newName = interaction.fields.getTextInputValue('channel_name');
    logger.debug(
      `${modalSource}: User ${interaction.user.tag} submitted new name "${newName}" for channel ${targetChannel.id}`
    );

    // Validation (matches Discord limits)
    if (!newName || newName.length < 1 || newName.length > 100) {
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].CROSS} Invalid channel name. Must be 1-100 characters.`,
      });
      return;
    }
    if (/^\s+$/.test(newName)) {
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].CROSS} Channel name cannot be only spaces.`,
      });
      return;
    }
    // Add any other reserved name checks if necessary

    await targetChannel.setName(newName, `Renamed by owner ${interaction.user.tag}`);
    logger.info(`Channel ${targetChannel.id} renamed to "${newName}" by ${interaction.user.tag}`);

    // Optional: Save user preference (assuming async)
    try {
      const userId = interaction.user.id;
      // await dataManager.setUserSetting(userId, 'channelNamePreference', newName);
      logger.debug(`Updated name preference for user ${userId}`);
    } catch (settingError) {
      logger.error(
        `Failed to save name preference for user ${interaction.user.id}: ${settingError}`
      );
    }

    // Final reply handled here
    await replyOrFollowUpEphemeral(interaction, {
      content: `${EMOJI[client.user.id].CHECK} Channel name updated to "${newName}".`,
    });
  } catch (error) {
    logger.error(`${modalSource}: Failed to handle name modal submission: ${error.message}`);
    await handleInteractionError(modalSource, error, interaction, client);
  }
};
