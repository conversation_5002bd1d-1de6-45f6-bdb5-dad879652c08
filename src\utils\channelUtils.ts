import { Guild, GuildMember, VoiceChannel, Client } from 'discord.js';
import logger from './logger'; // Assuming logger is in utils

// Assuming tempChannels is a Map<string, string> (channelId -> ownerId) on the client object
interface CustomClient extends Client {
  tempChannels: Map<string, string>;
}

/**
 * Finds the relevant temporary voice channel for a user interaction.
 * Checks the channel the user is currently in first, then checks if they own any tracked channel.
 * @param userId The ID of the user initiating the action.
 * @param guild The guild where the interaction took place.
 * @param client The custom client instance with tempChannels map.
 * @returns The VoiceChannel if found and relevant, otherwise null.
 */
export async function getUserVoiceChannel(
  userId: string,
  guild: Guild | null,
  client: CustomClient
): Promise<VoiceChannel | null> {
  if (!guild) {
    logger.warn('getUserVoiceChannel called without guild context.');
    return null;
  }

  let member: GuildMember | null = null;
  try {
    member = await guild.members.fetch(userId);
  } catch (error) {
    logger.warn(`Failed to fetch member ${userId} in guild ${guild.id}: ${error}`);
    return null; // Cannot proceed without member
  }

  // 1. Check current voice channel
  if (member.voice.channel && member.voice.channel instanceof VoiceChannel) {
    const currentChannel = member.voice.channel;
    if (client.tempChannels.has(currentChannel.id)) {
      logger.debug(
        `User ${member.user.username} (${userId}) is in their tracked voice channel ${currentChannel.name} (${currentChannel.id})`
      );
      return currentChannel;
    } else {
      logger.debug(
        `User ${member.user.username} (${userId}) is in non-tracked voice channel ${currentChannel.name} (${currentChannel.id})`
      );
    }
  }

  // 2. Check owned channels if not in a tracked one currently
  for (const [channelId, ownerId] of client.tempChannels.entries()) {
    if (ownerId === userId) {
      try {
        const ownedChannel = await guild.channels.fetch(channelId);
        if (ownedChannel && ownedChannel instanceof VoiceChannel) {
          logger.debug(
            `User ${member.user.username} (${userId}) owns tracked channel ${ownedChannel.name} (${ownedChannel.id})`
          );
          return ownedChannel;
        }
        // else: Channel might have been deleted or is not a voice channel anymore
      } catch (error) {
        // Channel might not exist anymore, log warning and continue checking
        logger.warn(`Error fetching owned channel ${channelId} for user ${userId}: ${error}`);
        // If the channel is gone, we should probably remove it from tempChannels
        client.tempChannels.delete(channelId);
      }
    }
  }

  // 3. No relevant channel found
  logger.debug(
    `No relevant tracked voice channel found for user ${member.user.username} (${userId}) in guild ${guild.id}`
  );
  return null;
}
