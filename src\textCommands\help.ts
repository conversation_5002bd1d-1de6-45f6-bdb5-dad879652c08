/**
 * Help Command
 * Displays available text commands and their usage
 */
import { EmbedBuilder } from 'discord.js';
import dataManager from '../utils/dataManager';
import { isDeveloper } from '../constants/devs';
import logger from '../utils/logger';
import { EMOJI } from '../constants/emoji';

// Map to store prefixes per guild for quick access
const guildPrefixes = new Map();

// Subscribe to prefix changes
dataManager.on('guildPrefixChanged', (guildId, newPrefix) => {
  guildPrefixes.set(guildId, newPrefix);
});

// Subscribe to guild settings changes
dataManager.on('guildSettingsChanged', (guildId, settings) => {
  if (settings.prefix) {
    guildPrefixes.set(guildId, settings.prefix);
  }
});

export const name = 'help';
export const description = 'Displays available text commands and their usage';
export const usage = 'help [command_name]';
export const devOnly = false;
export const adminOnly = false;

export const execute = async (message, args, client) => {
  try {
    // Get the prefix for this guild - check map first for performance
    let prefix = guildPrefixes.get(message.guild.id);
    if (!prefix) {
      // If not in map, get from database and store for future use
      prefix = await dataManager.getGuildPrefix(message.guild.id);
      guildPrefixes.set(message.guild.id, prefix);
    }

    // Check if specific command info was requested
    if (args.length > 0) {
      const commandName = args[0].toLowerCase();
      const command = client.textCommands.get(commandName);

      if (!command) {
        return message.reply({
          content: `${EMOJI[client.user.id].CROSS} Command not found: \`\`\`${commandName}\`\`\``,
          allowedMentions: { repliedUser: false },
        });
      }

      // Check if user has permission to see this command
      if (command.devOnly && !isDeveloper(message.author.id)) {
        return message.reply({
          content: `${EMOJI[client.user.id].CROSS} You do not have permission to view this command.`,
          allowedMentions: { repliedUser: false },
        });
      }

      // Create command info embed
      const embed = new EmbedBuilder()
        .setColor(0x5865f2) // Discord Blurple
        .setTitle(`Command: ${command.name}`)
        .setDescription(
          `Prefix: \`\`\`${prefix}\`\`\`\n${command.description || 'No description available'}`
        )
        .setFooter({
          text: `Requested by ${message.author.tag}`,
          iconURL: message.author.displayAvatarURL(),
        })
        .setTimestamp();

      // Add usage if available
      if (command.usage) {
        embed.addFields({
          name: 'Usage',
          value: `\`\`\`${prefix}${command.usage}\`\`\``,
        });
      }

      // Add permission info
      let permissionInfo = 'Everyone';
      if (command.devOnly) {
        permissionInfo = 'Bot Developer only';
      } else if (command.adminOnly) {
        permissionInfo = 'Server Administrator only';
      }

      embed.addFields({
        name: 'Permission',
        value: permissionInfo,
      });

      // Special handling for prefix command
      if (commandName === 'prefix') {
        embed.addFields({
          name: 'Usage',
          value:
            '```\n' +
            `${prefix}prefix\n` +
            'Show current prefix\n\n' +
            `${prefix}prefix set <new_prefix>\n` +
            'Change the prefix' +
            '```',
        });
      }

      return message.reply({
        embeds: [embed],
        allowedMentions: { repliedUser: false },
      });
    }

    // Group commands by permission level
    const commandGroups = {
      regular: [],
      admin: [],
      dev: [],
    };

    // Filter commands based on user's permission level
    for (const [_, command] of client.textCommands) {
      if (command.devOnly) {
        if (isDeveloper(message.author.id)) {
          commandGroups.dev.push(command);
        }
      } else if (command.adminOnly) {
        if (message.member.permissions.has('Administrator') || isDeveloper(message.author.id)) {
          commandGroups.admin.push(command);
        }
      } else {
        commandGroups.regular.push(command);
      }
    }

    // Create main help embed
    const embed = new EmbedBuilder()
      .setColor(0x5865f2) // Discord Blurple
      .setTitle('MyVC Bot Commands')
      .setDescription(
        `Use \`${prefix}help <command>\` for detailed information about a specific text command. For a general overview of bot features, try the \`/help\` slash command.\nCurrent prefix: \`\`\`${prefix}\`\`\``
      )
      .setThumbnail(client.user.displayAvatarURL())
      .setFooter({
        text: `Requested by ${message.author.tag}`,
        iconURL: message.author.displayAvatarURL(),
      })
      .setTimestamp();

    // Add regular commands
    if (commandGroups.regular.length > 0) {
      const regularCommands = commandGroups.regular
        .sort((a, b) => a.name.localeCompare(b.name))
        .map(cmd => `\`${prefix}${cmd.name}\`: ${cmd.description || 'No description available'}`)
        .join('\n');

      embed.addFields({
        name: '📋 Regular Commands',
        value: regularCommands,
      });
    }

    // Add admin commands
    if (commandGroups.admin.length > 0) {
      const adminCommands = commandGroups.admin
        .sort((a, b) => a.name.localeCompare(b.name))
        .map(cmd => `\`${prefix}${cmd.name}\`: ${cmd.description || 'No description available'}`)
        .join('\n');

      embed.addFields({
        name: '🔒 Administrator Commands',
        value: adminCommands,
      });
    }

    // Add developer commands
    if (commandGroups.dev.length > 0) {
      const devCommands = commandGroups.dev
        .sort((a, b) => a.name.localeCompare(b.name))
        .map(cmd => `\`${prefix}${cmd.name}\`: ${cmd.description || 'No description available'}`)
        .join('\n');

      embed.addFields({
        name: '⚙️ Developer Commands',
        value: devCommands,
      });
    }

    return message.reply({
      embeds: [embed],
      allowedMentions: { repliedUser: false },
    });
  } catch (error) {
    logger.error('Error in help command:', error);
    return message
      .reply({
        content: 'An error occurred while fetching help information.',
        allowedMentions: { repliedUser: false },
      })
      .catch(err => logger.error('Error sending error message:', err));
  }
};
