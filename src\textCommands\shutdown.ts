/**
 * Shutdown Command
 * Allows bot developers to shut down the bot gracefully
 */
import { EmbedBuilder, ActivityType } from 'discord.js';
import { Message, Client } from 'discord.js';
import logger from '../utils/logger';
import dataManager from '../utils/dataManager';

export const name = 'shutdown';
export const description = 'Shuts down the bot gracefully';
export const usage = 'shutdown [reason]';
export const devOnly = true; // Only developers can use this command
export const adminOnly = false;

export const execute = async (message: Message, args: string[], client: Client) => {
  try {
    const reason = args.length > 0 ? args.join(' ') : 'No reason provided';

    // Create shutdown embed
    const embed = new EmbedBuilder()
      .setColor(0xff0000)
      .setTitle('Bot Shutdown')
      .setDescription('The bot is shutting down...')
      .addFields(
        { name: 'Reason', value: reason },
        { name: 'Initiated By', value: `${message.author.tag} (${message.author.id})` }
      )
      .setFooter({
        text: 'Performing graceful shutdown',
        iconURL: client.user.displayAvatarURL(),
      })
      .setTimestamp();

    // Send shutdown message
    await message.reply({ embeds: [embed], allowedMentions: { repliedUser: false } });

    // Log the shutdown
    logger.warn(
      `Bot is shutting down - Initiated by ${message.author.tag} (${message.author.id}). Reason: ${reason}`
    );

    // Change status to indicate shutdown
    await client.user.setStatus('dnd');
    await client.user.setActivity('Shutting down...', { type: ActivityType.Playing });

    // Save all data before shutdown
    logger.info('Saving all data before shutdown...');

    // Save user settings
    if (global.saveUserSettings) {
      global.saveUserSettings();
    }

    // Clean up other resources
    if (dataManager.cleanup) {
      dataManager.cleanup();
    }

    // Wait a moment for all saving to complete
    setTimeout(() => {
      logger.info('Shutdown complete. Exiting process.');
      process.exit(0);
    }, 1500);
  } catch (error) {
    logger.error('Error in shutdown command:', error);
    message
      .reply({
        content: 'An error occurred while shutting down the bot.',
        allowedMentions: { repliedUser: false },
      })
      .catch(err => logger.error('Error sending error message:', err));
  }
};
