/**
 * Guild Logger - Specialized logger for guild-related events
 *
 * This utility logs guild join and leave events to a dedicated log file
 * in the data/logs directory for better tracking and analysis.
 * Also sends events to Discord webhook if configured.
 */
import fs from 'fs';
import path from 'path';
import logger from './logger';
// Import webhook utility (using dynamic import to avoid circular dependencies)
let webhookUtil: any = null;

// We'll load the webhook utility dynamically to avoid circular dependencies
const loadWebhookUtil = async () => {
  if (!webhookUtil) {
    try {
      // Dynamic import to avoid circular dependency
      const module = await import('./webhook');
      webhookUtil = module.default;
    } catch (error) {
      console.error('Failed to load webhook utility:', error);
    }
  }
  return webhookUtil;
};

// Log directory for guild events
const GUILD_LOG_DIR = path.join(process.cwd(), 'data', 'logs');

// Ensure log directory exists
if (!fs.existsSync(GUILD_LOG_DIR)) {
  try {
    fs.mkdirSync(GUILD_LOG_DIR, { recursive: true });
    logger.info(`Created guild logs directory at ${GUILD_LOG_DIR}`);
  } catch (error) {
    logger.error(`Failed to create guild logs directory: ${error.message}`);
  }
}

/**
 * Format a date for logging
 * @returns {string} Formatted date string
 */
function getFormattedDate(): string {
  const now = new Date();
  return now.toISOString();
}

/**
 * Get the path to the guild events log file
 * @returns {string} Path to the log file
 */
function getGuildLogFilePath(): string {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');

  return path.join(GUILD_LOG_DIR, `guild-events-${year}-${month}.log`);
}

/**
 * Log a guild join event with detailed information
 * @param {Guild} guild - The guild that was joined
 */
export async function logGuildJoin(guild: any): Promise<void> {
  try {
    const timestamp = getFormattedDate();
    const logFilePath = getGuildLogFilePath();

    // Collect detailed information about the guild
    const guildInfo = {
      event: 'JOIN',
      timestamp,
      guildId: guild.id,
      guildName: guild.name,
      memberCount: guild.memberCount,
      ownerID: guild.ownerId,
      region: guild.preferredLocale,
      features: guild.features,
      channels: {
        total: guild.channels?.cache.size || 0,
        text: guild.channels?.cache.filter((c: any) => c.type === 0).size || 0,
        voice: guild.channels?.cache.filter((c: any) => c.type === 2).size || 0,
        categories: guild.channels?.cache.filter((c: any) => c.type === 4).size || 0,
      },
      roles: guild.roles?.cache.size || 0,
      createdAt: guild.createdAt,
    };

    // Format the log entry
    const logEntry = JSON.stringify(guildInfo, null, 0);

    // Write to the log file
    fs.appendFileSync(logFilePath, `${logEntry}\n`);

    // Also log to the main logger
    logger.info(`Bot joined guild: ${guild.name} (${guild.id}) with ${guild.memberCount} members`);

    // Send to webhook if available
    try {
      const webhook = await loadWebhookUtil();
      if (webhook && webhook.sendGuildWebhook) {
        // Don't await this to avoid blocking
        webhook.sendGuildWebhook('JOIN', guildInfo).catch(() => {
          // Silently fail if webhook sending fails
        });
      }
    } catch (error) {
      // Don't log this error to avoid potential issues
      console.error('Failed to send guild join to webhook:', error);
    }
  } catch (error) {
    logger.error(
      `Error logging guild join: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

/**
 * Log a guild leave event with detailed information
 * @param {Guild} guild - The guild that was left
 * @param {string} reason - The reason for leaving (kick, ban, or manual leave)
 */
export async function logGuildLeave(guild: any, reason: string = 'unknown'): Promise<void> {
  try {
    const timestamp = getFormattedDate();
    const logFilePath = getGuildLogFilePath();

    // Collect detailed information about the guild
    const guildInfo = {
      event: 'LEAVE',
      timestamp,
      guildId: guild.id,
      guildName: guild.name,
      memberCount: guild.memberCount,
      ownerID: guild.ownerId,
      reason: reason,
      joinedAt: guild.joinedAt,
      leftAt: new Date().toISOString(),
    };

    // Format the log entry
    const logEntry = JSON.stringify(guildInfo, null, 0);

    // Write to the log file
    fs.appendFileSync(logFilePath, `${logEntry}\n`);

    // Also log to the main logger
    logger.info(`Bot left guild: ${guild.name} (${guild.id}), reason: ${reason}`);

    // Send to webhook if available
    try {
      const webhook = await loadWebhookUtil();
      if (webhook && webhook.sendGuildWebhook) {
        // Don't await this to avoid blocking
        webhook.sendGuildWebhook('LEAVE', guildInfo).catch(() => {
          // Silently fail if webhook sending fails
        });
      }
    } catch (error) {
      // Don't log this error to avoid potential issues
      console.error('Failed to send guild leave to webhook:', error);
    }
  } catch (error) {
    logger.error(
      `Error logging guild leave: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

export default {
  logGuildJoin,
  logGuildLeave,
};
