import {
  <PERSON>lashCommandBuilder,
  EmbedBuilder,
  ChatInputCommandInteraction,
  Client,
  MessageFlags,
} from 'discord.js';
import dataManager from '../utils/dataManager'; // Import dataManager to get prefix
import logger from '../utils/logger';

// Map to store prefixes per guild for quick access
const guildPrefixes = new Map();

// Subscribe to prefix changes
dataManager.on('guildPrefixChanged', (guildId, newPrefix) => {
  guildPrefixes.set(guildId, newPrefix);
  logger.info(`Slash help command: Prefix for guild ${guildId} updated to ${newPrefix}`);
});

// Subscribe to guild settings changes
dataManager.on('guildSettingsChanged', (guildId, settings) => {
  if (settings.prefix) {
    guildPrefixes.set(guildId, settings.prefix);
    logger.info(
      `Slash help command: Prefix for guild ${guildId} updated to ${settings.prefix} via settings change`
    );
  }
});

export const data = new SlashCommandBuilder()
  .setName('help')
  .setDescription('Shows basic information about how to use the MyVC bot');

export const execute = async (interaction: ChatInputCommandInteraction, client: Client) => {
  // Get the current server's prefix - check map first for performance
  const guildId = interaction.guildId;
  let prefix = guildPrefixes.get(guildId);

  if (!prefix) {
    // If not in map, get from database and store for future use
    prefix = await dataManager.getGuildPrefix(guildId);
    guildPrefixes.set(guildId, prefix);
  }

  // Create a minimal help embed
  const helpEmbed = new EmbedBuilder()
    .setColor(0x5865f2) // Discord Blurple
    .setTitle('MyVC Bot Help')
    .setDescription('Create and manage your own temporary voice channels!')
    .setThumbnail(client.user.displayAvatarURL())
    .addFields(
      {
        name: '🎙️ Creating Temporary Channels',
        value:
          'Join the designated "➕ JOIN_TO_CREATE" channel to automatically create your own voice channel.\n\n' +
          'You can control your channel setting from <#interface_channel_id> channel',
        inline: false,
      },
      {
        name: '📝 Server Information',
        value: `Current text command prefix: \`${prefix}\`\n\nUse \`${prefix}help\` for a list of text commands.`,
        inline: false,
      }
    )
    .setFooter({ text: 'Your voice, your space!', iconURL: client.user.displayAvatarURL() })
    .setTimestamp();

  // Send the minimal help embed
  await interaction.reply({ embeds: [helpEmbed], flags: MessageFlags.Ephemeral });
};
