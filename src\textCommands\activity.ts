/**
 * Activity Command
 * Allows bot developers to change the bot's activity and status
 */
import { EmbedBuilder, ActivityType, PresenceUpdateStatus } from 'discord.js';
import logger from '../utils/logger';

export const name = 'activity';
export const description = "Change the bot's activity and status";
export const usage = 'activity <status> <type> <name>';
export const devOnly = true; // Only developers can use this command
export const adminOnly = false;

export const execute = async (message, args, client) => {
  try {
    // Check if arguments were provided
    if (args.length < 3) {
      // Send usage information
      return message.reply({
        content: [
          '**Usage:** `activity <status> <type> <name>`',
          '',
          '**Available Statuses:**',
          '`online`, `idle`, `dnd`, `invisible`',
          '',
          '**Available Activity Types:**',
          '`playing`, `watching`, `listening`, `competing`, `streaming`',
          '',
          '**Example:**',
          '`activity online playing with temporary channels`',
        ].join('\n'),
        allowedMentions: { repliedUser: false },
      });
    }

    // Parse arguments
    const statusArg = args[0].toLowerCase();
    const typeArg = args[1].toLowerCase();
    const name = args.slice(2).join(' ');

    // Map the status string to PresenceUpdateStatus enum
    const statusMap: { [key: string]: PresenceUpdateStatus } = {
      online: PresenceUpdateStatus.Online,
      idle: PresenceUpdateStatus.Idle,
      dnd: PresenceUpdateStatus.DoNotDisturb,
      invisible: PresenceUpdateStatus.Invisible,
      offline: PresenceUpdateStatus.Invisible, // Map offline to invisible as per Discord API
    };
    const status = statusMap[statusArg];

    // Validate status
    if (!status) {
      return message.reply({
        content: `Invalid status. Available statuses: \`online\`, \`idle\`, \`dnd\`, \`invisible\``,
        allowedMentions: { repliedUser: false },
      });
    }

    // Map activity type string to ActivityType enum
    const typeMap: { [key: string]: ActivityType } = {
      playing: ActivityType.Playing,
      streaming: ActivityType.Streaming,
      listening: ActivityType.Listening,
      watching: ActivityType.Watching,
      competing: ActivityType.Competing,
    };
    const type = typeMap[typeArg];

    // Validate activity type
    if (type === undefined) {
      return message.reply({
        content: `Invalid activity type. Available types: \`playing\`, \`watching\`, \`listening\`, \`competing\`, \`streaming\``,
        allowedMentions: { repliedUser: false },
      });
    }

    // Set the activity
    await client.user.setPresence({
      activities: [
        {
          name,
          type: type, // Use the enum value
        },
      ],
      status: status, // Use the enum value
    });

    // Create success embed
    const embed = new EmbedBuilder()
      .setColor(0x00ff00)
      .setTitle('Bot Activity Updated')
      .addFields(
        { name: 'Status', value: statusArg, inline: true },
        { name: 'Activity Type', value: typeArg, inline: true },
        { name: 'Activity Name', value: name, inline: true }
      )
      .setFooter({
        text: `Changed by ${message.author.tag}`,
        iconURL: message.author.displayAvatarURL(),
      })
      .setTimestamp();

    await message.reply({ embeds: [embed], allowedMentions: { repliedUser: false } });

    logger.info(`Bot activity updated by ${message.author.tag}: ${statusArg} ${typeArg} "${name}"`);
  } catch (error) {
    logger.error('Error in activity command:', error);
    message
      .reply({
        content: "An error occurred while updating the bot's activity.",
        allowedMentions: { repliedUser: false },
      })
      .catch(err => logger.error('Error sending error message:', err));
  }
};
