# TempVoice Discord Bot

A Discord bot that creates temporary voice channels when users join a designated "join to create" channel.

## Features

- **Temporary Voice Channels**: Automatically creates voice channels when users join a designated channel
- **User Settings**: Remembers user preferences for channel names and settings
- **Automatic Cleanup**: Deletes empty channels when everyone leaves
- **Waiting Rooms**: Create waiting rooms where users can join before being moved to your channel
- **Channel Privacy**: Make channels private, locked, or public
- **User Management**: Trust, block, kick, or invite specific users
- **Channel Customization**: Change name, user limit, and region
- **Permissions Management**: Handles permissions even in complex role hierarchies
- **Fast Performance**: Optimized for minimal API calls and quick response times
- **Guild Event Logging**: Tracks when the bot is added to or removed from servers

## Installation

1. Clone this repository:
   ```bash
   git clone https://github.com/yourusername/tempvoice.git
   cd tempvoice
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create a `.env` file:
   ```bash
   cp .env.example .env
   ```
   Then edit `.env` with your configuration:
   - `TOKEN`: Your Discord bot token
   - `CLIENT_ID`: Your Discord application ID
   - `MONGODB_URI`: Your MongoDB connection string
   - Other settings as needed

4. Register bot commands:
   ```bash
   npm run deploy
   ```

## Running the Bot

### Development Mode
For development with auto-restart on file changes:
```bash
npm run dev
```

### Production Mode
For normal production use:
```bash
npm start
```

### Sharded Mode (for large bots)
For running with sharding enabled:
```bash
npm run start:sharded
```

## Environment Configuration

The bot uses the following environment variables:

- `TOKEN`: Discord bot token
- `CLIENT_ID`: Discord application ID
- `MONGODB_URI`: MongoDB connection string
- `CONSOLE_LOG_LEVEL`: Logging level (ERROR, WARN, INFO, DEBUG)
- `ERROR_WEBHOOK_URL`: Discord webhook URL for error logging (optional)
- `GUILD_WEBHOOK_URL`: Discord webhook URL for guild event logging (optional)
- `SHARD_COUNT`: Number of shards (1 for development, 'auto' for production)
- `SHARDED`: Whether to run in sharded mode (false for development, true for production)

## Bot Permissions

Required permissions:
- Manage Channels
- Manage Roles
- View Channels
- Connect
- Move Members
- Send Messages
- Embed Links

Invite link format:
```
https://discord.com/oauth2/authorize?client_id=YOUR_CLIENT_ID&permissions=16796753&scope=bot+applications.commands
```

## Commands

### Slash Commands
- `/setup` - Set up the TempVoice system in your server
- `/help` - Display help information
- `/ping` - Check if the bot is online

### Text Commands
- `prefix` - View or change the bot's command prefix
- `help` - Display help information for text commands

## Troubleshooting

Common issues and solutions:
1. **Bot can't create channels**
   - Ensure the bot has "Manage Channels" permission
   - Check the bot's role position in the server hierarchy

2. **Development mode not working**
   - Ensure `SHARD_COUNT=1` and `SHARDED=false` in `.env`
   - Check for TypeScript compilation errors

3. **Database connection issues**
   - Verify MongoDB connection string in `.env`
   - Check if MongoDB service is running

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## Guild Event Logging

The bot logs detailed information about guild join and leave events to help track usage and diagnose issues:

- Logs are stored in the `data/logs` directory as monthly files (e.g., `guild-events-2023-05.log`)
- Each event includes detailed information about the guild, such as member count, channel count, and more
- For leave events, the bot attempts to determine if it was kicked, banned, or left voluntarily
- Events can also be sent to a Discord channel via webhook for real-time monitoring

To view the guild logs, use the included script:

```bash
# View all guild events
ts-node scripts/view-guild-logs.ts

# Filter by guild ID
ts-node scripts/view-guild-logs.ts --guild 123456789012345678

# Filter by event type (JOIN or LEAVE)
ts-node scripts/view-guild-logs.ts --event JOIN

# Filter by month
ts-node scripts/view-guild-logs.ts --month 2023-05
```

You can also use the JavaScript version if you prefer:

```bash
node scripts/view-guild-logs.js
```

### Discord Webhook Integration

The bot can send logs to Discord channels via webhooks:

1. **Error Logging**: All errors are sent to a dedicated Discord channel
2. **Guild Event Logging**: Server join/leave events are sent to a separate channel

To set up webhook logging:

1. Create webhooks in your Discord server:
   - Go to a channel's settings → Integrations → Webhooks → New Webhook
   - Copy the webhook URL

2. Add the webhook URLs to your `.env` file:
   ```
   ERROR_WEBHOOK_URL=https://discord.com/api/webhooks/123456789012345678/abcdefghijklmnopqrstuvwxyz1234567890-ABCDEFG
   GUILD_WEBHOOK_URL=https://discord.com/api/webhooks/123456789012345678/abcdefghijklmnopqrstuvwxyz1234567890-ABCDEFG
   ```

   Important notes about webhook URLs:
   - Use the **exact** URL provided by Discord when you create the webhook
   - The URL must include both the webhook ID and token (the two parts after `/webhooks/`)
   - Do not add any extra parameters or modify the URL in any way
   - Each webhook should be used for only one purpose (don't use the same URL for both error and guild logging)

3. Restart the bot

If webhook URLs are not provided or are invalid, the bot will continue to function normally without sending webhook messages.

### Troubleshooting Webhook Issues

If you're experiencing issues with the webhook integration, check the following:

1. **Verify the webhook URL**: Make sure the webhook URL is correct and complete. You can test it by sending a message manually:
   ```bash
   curl -X POST -H "Content-Type: application/json" -d '{"content":"Test message"}' https://discord.com/api/webhooks/your_webhook_id/your_webhook_token
   ```

2. **Check webhook permissions**: Ensure the webhook has permission to send messages in the channel.

3. **Inspect the logs**: Look for webhook-related warnings in the bot logs, which may provide more details about the issue.

4. **Recreate the webhook**: If all else fails, try deleting the webhook in Discord and creating a new one.

5. **Common errors**:
   - `405 Method Not Allowed`: Usually means the webhook URL is incorrect or the webhook has been deleted
   - `401 Unauthorized`: The webhook token is invalid
   - `429 Too Many Requests`: The bot is being rate limited by Discord

## License

ISC License
