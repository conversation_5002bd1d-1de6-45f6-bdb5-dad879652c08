/**
 * <PERSON><PERSON>t to clear MongoDB collections for MyVC Bot
 * Simple and safe database clearing utility
 */

import mongoose from 'mongoose';
import logger from '../utils/logger';
import readline from 'readline';
require('dotenv').config();

const MONGODB_URI = process.env.MONGODB_URI || '';

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

// Promisify readline question
const question = (query: string): Promise<string> => {
  return new Promise(resolve => {
    rl.question(query, answer => {
      resolve(answer);
    });
  });
};

/**
 * Clear the MongoDB database with simple safety checks
 */
async function clearDatabase() {
  try {
    // Connect to MongoDB
    logger.info('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    logger.info('Connected to MongoDB successfully');

    // Get all collections
    const collections = await mongoose.connection.db.collections();
    logger.info(`Found ${collections.length} collections`);

    if (collections.length === 0) {
      console.log('No collections found in database.');
      rl.close();
      process.exit(0);
      return;
    }

    // List all collections
    console.log('\nAvailable collections:');
    collections.forEach((collection, index) => {
      console.log(`${index + 1}. ${collection.collectionName}`);
    });

    // Ask if user wants to delete all or select specific collections
    const selectionMode = await question(
      '\nDelete all collections or select specific ones? (all/select): '
    );

    let collectionsToDelete = [];

    if (selectionMode.toLowerCase() === 'all') {
      collectionsToDelete = collections;

      // Simple confirmation for all collections
      console.log('\n⚠️ WARNING: You are about to delete ALL collections in the database ⚠️');
      const confirm = await question('Are you sure? (yes/no): ');

      if (confirm.toLowerCase() !== 'yes') {
        console.log('Operation cancelled.');
        rl.close();
        process.exit(0);
        return;
      }
    } else {
      // Let user select specific collections
      console.log('\nEnter collection numbers to delete (comma-separated, e.g. 1,3,5):');
      const selection = await question('> ');

      // Parse selection
      const selectedIndices = selection
        .split(',')
        .map(s => s.trim())
        .filter(s => s !== '')
        .map(s => parseInt(s, 10) - 1) // Convert to 0-based index
        .filter(i => !isNaN(i) && i >= 0 && i < collections.length);

      if (selectedIndices.length === 0) {
        console.log('No valid collections selected. Operation cancelled.');
        rl.close();
        process.exit(0);
        return;
      }

      collectionsToDelete = selectedIndices.map(i => collections[i]);

      // Show confirmation for selected collections
      console.log('\nYou selected these collections to delete:');
      collectionsToDelete.forEach(collection => {
        console.log(`- ${collection.collectionName}`);
      });

      const confirm = await question('\nAre you sure? (yes/no): ');

      if (confirm.toLowerCase() !== 'yes') {
        console.log('Operation cancelled.');
        rl.close();
        process.exit(0);
        return;
      }
    }

    // Delete selected collections
    console.log('\nDeleting collections...');

    for (const collection of collectionsToDelete) {
      const name = collection.collectionName;
      console.log(`Clearing collection: ${name}`);
      const result = await collection.deleteMany({});
      console.log(`Collection ${name} cleared: ${result.deletedCount} documents removed`);
    }

    console.log(
      `\n<:check:1363393002151612427> ${collectionsToDelete.length} collections have been cleared`
    );

    // Close the connection
    await mongoose.connection.close();
    console.log('MongoDB connection closed');

    rl.close();
    process.exit(0);
  } catch (error) {
    logger.error('Error clearing database:', error);
    rl.close();
    process.exit(1);
  }
}

// Run the function
clearDatabase();
