import {
  ActionRowBuilder,
  ChannelType,
  PermissionFlagsBits,
  StringSelectMenuBuilder,
  VoiceChannel,
} from 'discord.js';
import dataManager from '../utils/dataManager';
import logger from '../utils/logger';
import { replyOrFollowUpEphemeral, handleInteractionError } from '../utils/interactionUtils';
import { EMOJI } from '../constants/emoji';

export const name = 'waiting';

// Helper to ensure waiting room exists and returns it
async function ensureWaitingRoom(interaction, client, ownerId, guild) {
  const settings = (await dataManager.getUserSettings(ownerId)) || {};
  let waitingRoomChannel = null;

  if (settings.waitingRoomId) {
    waitingRoomChannel = await guild.channels.fetch(settings.waitingRoomId).catch(() => null);
    if (!waitingRoomChannel || !(waitingRoomChannel instanceof VoiceChannel)) {
      logger.warn(
        `Waiting room ${settings.waitingRoomId} for user ${ownerId} not found or invalid type. Creating new one.`
      );
      settings.waitingRoomId = null;
    }
  }

  if (!waitingRoomChannel) {
    try {
      const guildSettings = await dataManager.getGuildSettings(guild.id);
      const parentCategory = guildSettings?.tempCategoryId || null;

      waitingRoomChannel = await guild.channels.create({
        name: `waiting-room-${interaction.user.username}`,
        type: ChannelType.GuildVoice,
        parent: parentCategory,
        permissionOverwrites: [
          {
            id: guild.roles.everyone.id,
            allow: [PermissionFlagsBits.Connect, PermissionFlagsBits.ViewChannel],
          },
          {
            id: ownerId,
            allow: [PermissionFlagsBits.ViewChannel],
          },
          {
            id: client.user.id,
            allow: [PermissionFlagsBits.ViewChannel],
          },
        ],
      });
      logger.info(`Created waiting room ${waitingRoomChannel.id} for user ${ownerId}`);
      settings.waitingRoomId = waitingRoomChannel.id;
      settings.hasWaitingRoom = true;
      await dataManager.saveUserSettings({ [ownerId]: settings });
    } catch (createError) {
      logger.error(`Failed to create waiting room for user ${ownerId}: ${createError.message}`);
      throw new Error('Failed to create waiting room channel.');
    }
  }
  return { waitingRoomChannel, settings };
}

// Called when the "Waiting" button is clicked
// Interaction is deferred, ownership & cooldown checked in interactionCreate.ts
export const execute = async (interaction, client, userChannel) => {
  // Sends the enable/disable menu
  try {
    const ownerId = interaction.user.id;
    const settings = (await dataManager.getUserSettings(ownerId)) || {};
    const isWaitingEnabled = settings.hasWaitingRoom ?? false;

    const waitingSelect = new StringSelectMenuBuilder()
      .setCustomId(`waiting_select_${userChannel.id}`) // Consistent ID format
      .setPlaceholder('Enable or disable waiting room')
      .addOptions([
        {
          label: 'Enable Waiting Room',
          description: 'Users must request to join your channel.',
          value: 'enable',
          default: isWaitingEnabled,
        },
        {
          label: 'Disable Waiting Room',
          description: 'Users can join freely (based on privacy settings).',
          value: 'disable',
          default: !isWaitingEnabled,
        },
      ]);

    const row: ActionRowBuilder<StringSelectMenuBuilder> =
      new ActionRowBuilder<StringSelectMenuBuilder>().addComponents(waitingSelect);

    // Use replyOrFollowUpEphemeral as interaction is deferred by the central handler
    await replyOrFollowUpEphemeral(interaction, {
      content: `Waiting Room is currently ${isWaitingEnabled ? '**Enabled**' : '**Disabled**'}. Choose an action:`,
      components: [row],
    });
  } catch (error) {
    logger.error(`Error sending waiting room select menu: ${error.message}`);
    await handleInteractionError('waiting.execute', error, interaction, client);
  }
};

// Called when enable/disable option is selected
export const handleSelectMenu = async (interaction, client, targetChannel) => {
  const menuSource = `waiting.handleSelectMenu:selectMenu:${interaction.customId}`;
  // Cooldown & ownership checked in interactionCreate.ts before calling this
  if (!interaction.isStringSelectMenu()) return; // Type guard

  try {
    const selectedValue = interaction.values[0];
    const ownerId = interaction.user.id;

    logger.debug(
      `${menuSource}: User ${ownerId} selected waiting room option "${selectedValue}" for channel ${targetChannel.id}`
    );

    if (selectedValue === 'enable') {
      const { waitingRoomChannel, settings } = await ensureWaitingRoom(
        interaction,
        client,
        ownerId,
        interaction.guild
      );

      if (!waitingRoomChannel) {
        await replyOrFollowUpEphemeral(interaction, {
          content: `${EMOJI[client.user.id].CROSS} Failed to create or find the waiting room channel.`,
          components: [],
        });
        return;
      }

      settings.hasWaitingRoom = true;
      await dataManager.saveUserSettings({ [ownerId]: settings });

      // Make main voice channel non-connectable for @everyone
      await targetChannel.permissionOverwrites.edit(
        interaction.guild.roles.everyone,
        {
          Connect: false,
        },
        `Waiting room enabled by owner ${ownerId}`
      );
      // Ensure owner and trusted users can still connect
      await targetChannel.permissionOverwrites.edit(ownerId, { Connect: true });
      const trustedUsers = settings.trustedUsers || [];
      for (const trustedId of trustedUsers) {
        await targetChannel.permissionOverwrites.edit(trustedId, { Connect: true }).catch(err => {
          logger.warn(
            `Failed to grant Connect permission to trusted user ${trustedId} for channel ${targetChannel.id}: ${err.message}`
          );
        });
      }

      logger.info(
        `Waiting room enabled for channel ${targetChannel.id} by owner ${ownerId}. Waiting room channel: ${waitingRoomChannel.id}`
      );
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].CHECK} Waiting room **Enabled**. Users must now request access via the "${waitingRoomChannel.name}" voice channel.`,
        components: [],
      });
    } else if (selectedValue === 'disable') {
      const settings = (await dataManager.getUserSettings(ownerId)) || {};
      let waitingRoomDeleted = false;

      if (settings.waitingRoomId) {
        const waitingRoomChannel = await interaction.guild.channels
          .fetch(settings.waitingRoomId)
          .catch(() => null);
        if (waitingRoomChannel) {
          await waitingRoomChannel
            .delete(`Waiting room disabled by owner ${ownerId}`)
            .then(() => {
              waitingRoomDeleted = true;
              logger.info(`Deleted waiting room channel ${settings.waitingRoomId}`);
            })
            .catch(delErr => {
              logger.warn(
                `Could not delete waiting room channel ${settings.waitingRoomId}: ${delErr.message}`
              );
            });
        }
      }

      settings.hasWaitingRoom = false;
      settings.waitingRoomId = null;
      await dataManager.saveUserSettings({ [ownerId]: settings });

      // Restore @everyone connect perms based on privacy
      const everyoneOverwrites = targetChannel.permissionOverwrites.cache.get(
        interaction.guild.roles.everyone.id
      );
      const canEveryoneView = everyoneOverwrites
        ? everyoneOverwrites.allow.has(PermissionFlagsBits.ViewChannel)
        : true;
      await targetChannel.permissionOverwrites.edit(
        interaction.guild.roles.everyone,
        {
          Connect: canEveryoneView,
        },
        `Waiting room disabled by owner ${ownerId}`
      );

      logger.info(`Waiting room disabled for channel ${targetChannel.id} by owner ${ownerId}.`);
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].CHECK} Waiting room **Disabled**.${waitingRoomDeleted ? ' The waiting room channel has been deleted.' : ''}`,
        components: [],
      });
    }
  } catch (error) {
    logger.error(`${menuSource}: Failed to handle waiting room select menu: ${error.message}`);
    await handleInteractionError(menuSource, error, interaction, client);
  }
};
