import {
  ActionRowBuilder,
  UserSelectMenuBuilder,
  ButtonBuilder,
  ButtonStyle,
  EmbedBuilder,
} from 'discord.js';
import dataManager from '../utils/dataManager';
import logger from '../utils/logger';
import { replyOrFollowUpEphemeral, handleInteractionError } from '../utils/interactionUtils';
import { EMOJI } from '../constants/emoji';
import { SERVER_INVITE_LINK } from '../constants';

export const name = 'invite';

// Called when the "Invite" button is clicked
// Interaction is deferred, ownership & cooldown checked in interactionCreate.ts
export const execute = async (interaction, client, userChannel) => {
  // Sends the user select menu
  try {
    const inviteSelect = new UserSelectMenuBuilder()
      .setCustomId(`invite_select_${userChannel.id}`) // Consistent ID format
      .setPlaceholder('Select user(s) to invite')
      .setMinValues(1)
      .setMaxValues(10); // Allow inviting multiple users

    const row: any = new ActionRowBuilder().addComponents(inviteSelect);

    // Use replyOrFollowUpEphemeral as interaction is deferred by the central handler
    await replyOrFollowUpEphemeral(interaction, {
      content: 'Select user(s) to invite to your channel. They will be automatically trusted:',
      components: [row],
    });
  } catch (error) {
    logger.error(`Error sending invite select menu: ${error.message}`);
    await handleInteractionError('invite.execute', error, interaction, client);
  }
};

// Called when users are selected from the menu sent above
export const handleSelectMenu = async (interaction, client, targetChannel) => {
  const menuSource = `invite.handleSelectMenu:selectMenu:${interaction.customId}`;
  // Cooldown & ownership checked in interactionCreate.ts before calling this
  if (!interaction.isUserSelectMenu()) return; // Type guard

  try {
    const selectedUsers = interaction.users; // Map<string, User>
    const ownerId = interaction.user.id;
    const invitedUserTags: string[] = [];
    const failedInviteTags: string[] = [];
    let trustErrorsOccurred = false;

    logger.debug(
      `${menuSource}: User ${ownerId} selected ${selectedUsers.size} user(s) to invite to channel ${targetChannel.id}`
    );

    // Fetch settings (assuming async)
    const settings = (await dataManager.getUserSettings(ownerId)) || { trustedUsers: [] };
    settings.trustedUsers = settings.trustedUsers || [];

    const channelUrl = `https://discord.com/channels/${interaction.guild.id}/${targetChannel.id}`;

    for (const [selectedUserId, selectedUser] of selectedUsers) {
      if (selectedUserId === ownerId) continue;
      if (selectedUser.bot) {
        logger.warn(`${menuSource}: User ${ownerId} attempted to invite bot ${selectedUser.tag}.`);
        failedInviteTags.push(`${selectedUser.tag} (bot)`);
        continue;
      }

      // 1. Trust the user
      try {
        if (!settings.trustedUsers.includes(selectedUserId)) {
          settings.trustedUsers.push(selectedUserId);
        }
        await targetChannel.permissionOverwrites.edit(
          selectedUserId,
          {
            ViewChannel: true,
            Connect: true,
            Speak: true,
            // Stream: true,
            // UseEmbeddedActivities: true,
          },
          `Trusted & Invited by channel owner ${interaction.user.tag}`
        );
        logger.debug(`Trusted user ${selectedUser.tag} (${selectedUserId}) for invite`);
      } catch (trustError) {
        logger.error(
          `${menuSource}: Failed to trust user ${selectedUserId} (${selectedUser.tag}) during invite: ${trustError.message}`
        );
        trustErrorsOccurred = true;
        failedInviteTags.push(`${selectedUser.tag} (permission error)`);
        continue;
      }

      // 2. Send DM invite
      const inviteEmbed = new EmbedBuilder()
        .setColor(0x5865f2)
        .setTitle('Voice Channel Invitation')
        .setDescription(`${interaction.user.tag} invited you to join their voice channel!`)
        .addFields(
          { name: 'Channel', value: targetChannel.name, inline: true },
          { name: 'Server', value: interaction.guild.name, inline: true },
          { name: 'Direct Link', value: `[Click here to join](${channelUrl})`, inline: false }
        )
        .setTimestamp();

      const inviteButton = new ButtonBuilder()
        .setLabel('Join Channel')
        .setStyle(ButtonStyle.Secondary)
        .setCustomId(`join_${targetChannel.id}`)
        .setEmoji(EMOJI[client.user.id].JOIN);

      const supportButton = new ButtonBuilder()
        .setLabel('Support Server')
        .setStyle(ButtonStyle.Link)
        .setURL(SERVER_INVITE_LINK)
        .setEmoji(EMOJI[client.user.id].SUPPORT);

      const inviteRow: any = new ActionRowBuilder().addComponents(inviteButton, supportButton);

      try {
        const member = await interaction.guild.members.fetch(selectedUserId).catch(() => null);
        if (member) {
          await member.send({
            content: `Invitation from ${interaction.user.tag}:`,
            embeds: [inviteEmbed],
            components: [inviteRow],
          });
          invitedUserTags.push(selectedUser.tag);
          logger.debug(`Sent invite DM to ${selectedUser.tag} (${selectedUserId})`);
        } else {
          logger.warn(`${menuSource}: Could not fetch member ${selectedUserId} to send DM invite.`);
          failedInviteTags.push(`${selectedUser.tag} (not found)`);
        }
      } catch (dmError) {
        logger.warn(`${menuSource}: Failed to DM user ${selectedUserId}: ${dmError.message}`);
        failedInviteTags.push(`${selectedUser.tag} (DMs disabled?)`);
        invitedUserTags.push(`${selectedUser.tag} (trusted, DM failed)`);
      }
    }

    // Save updated settings (assuming async)
    // await dataManager.setUserSettings(ownerId, settings);
    logger.debug(`Updated trusted users settings for owner ${ownerId} after invites`);

    // Construct final reply
    let replyMessage = '';
    if (invitedUserTags.length > 0) {
      replyMessage += `${EMOJI[client.user.id].CHECK} Invited & Trusted: ${invitedUserTags.join(', ')}.`;
    }
    if (failedInviteTags.length > 0) {
      replyMessage +=
        (replyMessage ? '\n' : '') + `⚠️ Failed to invite/DM: ${failedInviteTags.join(', ')}.`;
    }
    if (trustErrorsOccurred) {
      replyMessage +=
        (replyMessage ? '\n' : '') + '⚠️ Errors occurred granting permissions for some users.';
    }
    if (!replyMessage) {
      replyMessage = 'No users were invited (bots/yourself cannot be invited).';
    }

    // Final reply handled here
    await replyOrFollowUpEphemeral(interaction, {
      content: replyMessage,
      components: [],
    });
  } catch (error) {
    logger.error(`${menuSource}: Failed to handle invite select menu: ${error.message}`);
    await handleInteractionError(menuSource, error, interaction, client);
  }
};
