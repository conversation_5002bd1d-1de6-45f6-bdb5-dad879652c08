import { ActionRowBuilder, StringSelectMenuBuilder } from 'discord.js';
import dataManager from '../utils/dataManager';
import logger from '../utils/logger';
import { replyOrFollowUpEphemeral, handleInteractionError } from '../utils/interactionUtils';
import { EMOJI } from '../constants/emoji';

export const name = 'untrust';

// Called when the "Untrust" button is clicked
// Interaction is deferred, ownership checked in interactionCreate.ts
// NOTE: This action should not have cooldowns
export const execute = async (interaction, client, userChannel) => {
  // Sends the user select menu listing currently trusted users
  try {
    const ownerId = interaction.user.id;
    // Fetch settings (assuming async)
    const settings = (await dataManager.getUserSettings(ownerId)) || { trustedUsers: [] };
    const trustedUserIds = settings.trustedUsers || [];

    if (trustedUserIds.length === 0) {
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].UNTRUST} You have no users currently trusted.`,
      });
      return;
    }

    // Fetch user objects to validate and potentially provide better context
    const validTrustedUsers = [];
    for (const userId of trustedUserIds) {
      try {
        // Skip the bot itself
        if (userId === client.user.id) continue;

        const user = await client.users.fetch(userId);
        validTrustedUsers.push({
          id: userId,
          tag: user.tag,
          username: user.username,
        });
      } catch {
        logger.warn(`Removing non-existent user ${userId} from trusted list of ${ownerId}`);
        settings.trustedUsers = settings.trustedUsers.filter(id => id !== userId);
        // Consider saving cleaned settings here
      }
    }

    if (validTrustedUsers.length === 0) {
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].UNTRUST} No valid trusted users found to untrust.`,
      });
      return;
    }

    // Create a string select menu with trusted users as options
    const untrustSelect = new StringSelectMenuBuilder()
      .setCustomId(`untrust_select_${userChannel.id}`)
      .setPlaceholder('Select user(s) to untrust')
      .setMinValues(1)
      .setMaxValues(validTrustedUsers.length > 10 ? 10 : validTrustedUsers.length);

    // Add each trusted user as an option
    for (const user of validTrustedUsers) {
      untrustSelect.addOptions({
        label: user.tag,
        description: `User ID: ${user.id}`,
        value: user.id,
      });
    }

    const row: any = new ActionRowBuilder().addComponents(untrustSelect);

    // Use replyOrFollowUpEphemeral as interaction is deferred by the central handler
    await replyOrFollowUpEphemeral(interaction, {
      content: 'Select user(s) to remove from your trusted list:',
      components: [row],
    });
  } catch (error) {
    logger.error(`Error sending untrust select menu: ${error.message}`);
    await handleInteractionError('untrust.execute', error, interaction, client);
  }
};

// Called when users are selected from the menu sent above
// NOTE: This action should not have cooldowns
export const handleSelectMenu = async (interaction, client, targetChannel) => {
  const menuSource = `untrust.handleSelectMenu:selectMenu:${interaction.customId}`;
  // Ownership checked in interactionCreate.ts before calling this
  if (!interaction.isStringSelectMenu()) return; // Type guard

  try {
    const selectedUserIds = interaction.values;
    const ownerId = interaction.user.id;
    const untrustedUserTags: string[] = [];
    let errorsOccurred = false;

    logger.debug(
      `${menuSource}: User ${ownerId} selected ${selectedUserIds.length} user(s) to untrust for channel ${targetChannel.id}`
    );

    // Fetch settings (assuming async)
    const settings = (await dataManager.getUserSettings(ownerId)) || { trustedUsers: [] };
    settings.trustedUsers = settings.trustedUsers || [];
    let settingsChanged = false;

    for (const selectedUserId of selectedUserIds) {
      if (!settings.trustedUsers.includes(selectedUserId)) {
        logger.warn(
          `${menuSource}: User ${selectedUserId} selected for untrust, but not found in settings for owner ${ownerId}.`
        );
        continue;
      }

      try {
        // Fetch user info for display
        const selectedUser = await client.users.fetch(selectedUserId).catch(() => null);

        // Remove from settings
        settings.trustedUsers = settings.trustedUsers.filter(id => id !== selectedUserId);
        settingsChanged = true;
        logger.debug(`Removed ${selectedUserId} from trusted list for owner ${ownerId}`);

        // Check if user is in the guild before trying to delete permission overwrite
        let isInGuild = false;
        try {
          // Try to fetch the member to see if they're in the guild
          const guildMember = await targetChannel.guild.members
            .fetch(selectedUserId)
            .catch(() => null);
          isInGuild = !!guildMember;
        } catch (error) {
          logger.debug(
            `${menuSource}: Could not determine if user ${selectedUserId} is in guild: ${error.message}`
          );
        }

        if (isInGuild) {
          // Only try to delete permission overwrite if user is in the guild
          const overwrite = targetChannel.permissionOverwrites.cache.get(selectedUserId);
          if (overwrite) {
            await overwrite.delete(`Untrusted by channel owner ${interaction.user.tag}`);
            logger.debug(`Removed permission overwrite for untrusted user ${selectedUserId}`);
          } else {
            logger.debug(
              `${menuSource}: No permission overwrite found for user ${selectedUserId} in channel ${targetChannel.id}, but user is in guild.`
            );
          }
        } else {
          logger.debug(
            `${menuSource}: User ${selectedUserId} is not in guild, skipping permission overwrite deletion.`
          );
        }

        untrustedUserTags.push(selectedUser ? selectedUser.tag : selectedUserId);
      } catch (userUntrustError) {
        logger.error(
          `${menuSource}: Failed to untrust user ${selectedUserId}: ${userUntrustError.message}`
        );
        errorsOccurred = true;
      }
    }

    // Save updated settings if changed
    if (settingsChanged) {
      try {
        const settingsObj = { [ownerId]: settings };
        await dataManager.saveUserSettings(settingsObj);
        logger.debug(
          `Updated trusted users settings for owner ${ownerId} in MongoDB after untrust`
        );
      } catch (error) {
        logger.error(`Failed to save updated trusted users to MongoDB: ${error.message}`);
        // Fall back to queue-based saving
        global.saveUserSettings(ownerId);
        logger.debug(`Falling back to queue-based saving for untrust operation`);
      }
    }

    // Construct final reply
    let replyMessage = '';
    if (untrustedUserTags.length > 0) {
      replyMessage += `${EMOJI[client.user.id].CHECK} Untrusted ${untrustedUserTags.join(', ')}.`;
    }
    if (errorsOccurred) {
      replyMessage +=
        (replyMessage ? '\n' : '') +
        '⚠️ Errors occurred while untrusting some users. Check bot permissions.';
    }
    if (!replyMessage) {
      replyMessage =
        'No users were untrusted (possibly due to errors or selecting non-trusted users).';
    }

    // Final reply handled here
    await replyOrFollowUpEphemeral(interaction, {
      content: replyMessage,
      components: [],
    });
  } catch (error) {
    logger.error(`${menuSource}: Failed to handle untrust select menu: ${error.message}`);
    await handleInteractionError(menuSource, error, interaction, client);
  }
};
