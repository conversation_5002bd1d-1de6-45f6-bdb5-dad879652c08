/**
 * MyVC Discord Bot
 * Optimized for 100+ concurrent users
 */
require('dotenv').config();
import { Client, Collection, Events, GatewayIntentBits, Partials } from 'discord.js';
import fs from 'fs';
import path from 'path';

// Import custom utilities
import logger from './utils/logger';
import { ExtendedClient } from './types';
import dataManager from './utils/dataManager';
import { createEmbed, formatBytes, formatUptime } from './utils/helpers';

// Check if running as a shard or standalone
const isSharded = process.env.SHARDS !== undefined || Bo<PERSON>an(process.env.SHARDED);

// Import constants
import { INTERVALS } from './constants';

// Performance tracking
let totalSaveOperations = 0;
let successfulSaveOperations = 0;
let totalMessagesProcessed = 0;
let totalCommandsUsed = 0;
const startTime = Date.now();

// Create the client instance with necessary intents
// Export client to make it available to other modules
export const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.GuildVoiceStates,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.DirectMessages,
  ],
  partials: [Partials.Channel, Partials.Message],
  // Increase message sweep interval and lifetime for performance with many users
  sweepers: {
    messages: {
      interval: 60, // Sweep every minute
      lifetime: 3600, // Only cache messages from the last hour
    },
  },
  // When not using the ShardingManager, set shardCount to 1
  // When using ShardingManager, these options are handled automatically
  // and don't need to be specified here
}) as ExtendedClient;

// Collections for client data
client.commands = new Collection();
client.textCommands = new Collection(); // For prefix commands
client.tempChannels = new Map(); // Maps channel IDs to user IDs
client.userSettings = new Map(); // Maps user IDs to channel settings
client.defaultSettings = new Map(); // Default settings for new channels
client.cooldowns = new Collection(); // For rate limiting commands

// Map to track empty waiting rooms and their timestamps
const emptyWaitingRooms = new Map(); // Maps waiting room IDs to timestamps when they became empty

// Save queue to track which users need their settings saved
const saveQueue = new Set();
let savingInProgress = false;
let lastSaveAttempt = 0;
const SAVE_RETRY_INTERVAL = 30000; // 30 seconds

/**
 * Queue user settings for saving
 * This batches saves to avoid excessive disk I/O
 * @param {string} userId - Optional user ID to prioritize
 * @param {boolean} immediate - Whether to try saving immediately
 */
function queueUserSettingsSave(userId = null, immediate = false) {
  if (userId) {
    saveQueue.add(userId);
  }

  const now = Date.now();
  const timeSinceLastSave = now - lastSaveAttempt;

  // If immediate save requested or enough time has passed since last attempt
  if (immediate || timeSinceLastSave >= SAVE_RETRY_INTERVAL) {
    // If saving is already in progress, the queue will be processed later
    if (!savingInProgress) {
      processSaveQueue();
    }
  }
}

/**
 * Process the user settings save queue
 * Limits writes to disk to avoid I/O bottlenecks
 */
async function processSaveQueue() {
  if (savingInProgress || saveQueue.size === 0) return;

  savingInProgress = true;
  lastSaveAttempt = Date.now();

  try {
    // If we have items in the queue, process them
    if (saveQueue.size > 0) {
      const success = await saveAllUserSettings();

      if (success) {
        // Clear the queue after successful save
        saveQueue.clear();
        logger.info('Successfully processed save queue');
      } else {
        logger.warn('Failed to process save queue, will retry later');
        // Schedule retry after delay
        setTimeout(() => {
          savingInProgress = false;
          processSaveQueue();
        }, SAVE_RETRY_INTERVAL);
        return;
      }
    }
  } catch (error) {
    logger.error('Failed to process save queue:', error);
    // Schedule retry after delay
    setTimeout(() => {
      savingInProgress = false;
      processSaveQueue();
    }, SAVE_RETRY_INTERVAL);
    return;
  } finally {
    savingInProgress = false;
  }

  // If new items were added to the queue while we were saving, process again
  if (saveQueue.size > 0) {
    setTimeout(processSaveQueue, 1000); // Small delay to avoid tight loop
  }
}

/**
 * Save all user settings to file
 * Converts Map to object for storage
 */
async function saveAllUserSettings() {
  totalSaveOperations++;

  try {
    // Validate user settings before saving
    for (const [userId, settings] of client.userSettings) {
      if (!settings || typeof settings !== 'object') {
        logger.warn(`Invalid settings detected for user ${userId}, skipping`);
        client.userSettings.delete(userId);
        continue;
      }
    }

    // Convert Map to object for MongoDB storage
    const settingsObj = {};
    for (const [userId, settings] of client.userSettings) {
      // Make a deep copy to avoid reference issues
      const settingsCopy = JSON.parse(JSON.stringify(settings));

      // Ensure null/undefined values don't cause issues in MongoDB
      for (const key in settingsCopy) {
        if (settingsCopy[key] === undefined) {
          delete settingsCopy[key];
        }
      }

      // Add last updated timestamp
      settingsCopy.lastUpdated = new Date();

      settingsObj[userId] = settingsCopy;
    }

    // Use dataManager to save settings to MongoDB
    const success = await dataManager.saveUserSettings(settingsObj);

    if (success) {
      successfulSaveOperations++;
      logger.info(
        `Successfully saved settings for ${Object.keys(settingsObj).length} users to MongoDB`
      );

      // Verify the save was successful by reading back from MongoDB
      try {
        const savedData = await dataManager.loadUserSettings();
        const savedCount = Object.keys(savedData).length;

        // Log sample of saved data for debugging
        if (Object.keys(savedData).length > 0) {
          const sampleUserId = Object.keys(savedData)[0];
          const sampleData = savedData[sampleUserId];
          logger.debug(
            `Sample saved user data for ${sampleUserId}: ${JSON.stringify({
              name: sampleData.name || 'N/A',
              isPrivate: sampleData.isPrivate || false,
              hasWaitingRoom: sampleData.hasWaitingRoom || false,
              userLimit: sampleData.userLimit || 0,
              permissionOverwrites: sampleData.permissionOverwrites
                ? sampleData.permissionOverwrites.length
                : 0,
            })}`
          );
        }

        if (savedCount !== Object.keys(settingsObj).length) {
          logger.warn(
            `Data verification mismatch: saved ${Object.keys(settingsObj).length} users but read back ${savedCount}`
          );
          return false;
        }
      } catch (error) {
        logger.error('Failed to verify saved data:', error);
        return false;
      }
    } else {
      logger.error('Failed to save user settings to MongoDB');
      return false;
    }

    return true;
  } catch (error) {
    logger.error('Failed to save user settings:', error);
    return false;
  }
}

/**
 * Make saveUserSettings function globally available
 * @param {string} userId - Optional user ID to prioritize
 */
global.saveUserSettings = queueUserSettingsSave;

/**
 * Load user settings from MongoDB
 */
async function loadUserSettings() {
  try {
    // Use dataManager to load settings from MongoDB
    const settingsObj = await dataManager.loadUserSettings();

    // Convert object to Map
    client.userSettings.clear(); // Clear existing settings to ensure we have fresh data

    for (const [userId, settings] of Object.entries(settingsObj)) {
      if (settings) {
        client.userSettings.set(userId, settings);
      }
    }

    logger.info(`Loaded settings for ${client.userSettings.size} users from MongoDB`);

    // Log a sample of loaded data for debugging
    if (client.userSettings.size > 0) {
      const sampleUserId = Array.from(client.userSettings.keys())[0];
      const sampleData = client.userSettings.get(sampleUserId);
      logger.debug(
        `Sample loaded user data for ${sampleUserId}: ${JSON.stringify({
          name: sampleData.name || 'N/A',
          isPrivate: sampleData.isPrivate || false,
          hasWaitingRoom: sampleData.hasWaitingRoom || false,
          userLimit: sampleData.userLimit || 0,
          permissionOverwrites: sampleData.permissionOverwrites
            ? sampleData.permissionOverwrites.length
            : 0,
        })}`
      );
    }
  } catch (error) {
    logger.error('Failed to load user settings from MongoDB:', error);
  }
}

/**
 * Saves bot performance stats to MongoDB
 */
async function saveBotStats() {
  try {
    const memoryUsage = process.memoryUsage();
    const stats = {
      memory: {
        rss: formatBytes(memoryUsage.rss),
        heapTotal: formatBytes(memoryUsage.heapTotal),
        heapUsed: formatBytes(memoryUsage.heapUsed),
        external: formatBytes(memoryUsage.external),
      },
      timestamp: new Date().toISOString(),
    };

    // Save stats to MongoDB
    try {
      await dataManager.saveBotStats(stats);
      logger.info('Successfully saved bot stats to MongoDB');
    } catch (error) {
      logger.error('Failed to save bot stats to MongoDB:', error);
    }
  } catch (error) {
    logger.error('Failed to collect bot stats:', error);
  }
}

/**
 * Clean up empty voice channels to avoid memory leaks
 * Also deletes empty channels if specified
 * @param {boolean} deleteEmptyChannels - Whether to delete empty channels
 */
async function cleanupEmptyVoiceChannels(deleteEmptyChannels = false) {
  try {
    let channelsRemoved = 0;
    let channelsDeleted = 0;
    let channelsChecked = 0;
    let waitingRoomsDeleted = 0;
    const deletionPromises = [];

    if (client.tempChannels.size === 0) {
      logger.debug('No temporary channels to check, skipping cleanup');

      // Even if no temp channels, we should still check for orphaned waiting rooms
      if (client.userSettings.size > 0) {
        logger.debug(
          `Checking ${client.userSettings.size} user settings for orphaned waiting rooms`
        );
        await cleanupOrphanedWaitingRooms();
      }

      return;
    }

    logger.debug(`Starting cleanup process, checking ${client.tempChannels.size} tracked channels`);

    // Get the guild settings to find only relevant channels - don't await, we'll use it later
    const guildSettingsPromise = dataManager.loadGuildSettings();

    // Collect users who still have voice channels
    const usersWithChannels = new Set();

    // Create a map of guilds to channels for faster lookup
    const guildChannelsMap = new Map();

    // Pre-populate the map with all guild channels for faster lookup
    for (const guild of client.guilds.cache.values()) {
      guildChannelsMap.set(guild.id, guild.channels.cache);
    }

    // First, check and delete waiting rooms that have been empty for 1 minute
    await checkEmptyWaitingRooms();

    // Process each channel in the tempChannels Map - use batch processing for better performance
    const channelEntries = Array.from(client.tempChannels.entries());
    const batchSize = 10; // Process 10 channels at a time

    for (let i = 0; i < channelEntries.length; i += batchSize) {
      const batch = channelEntries.slice(i, i + batchSize);

      // Process this batch in parallel
      await Promise.all(
        batch.map(async ([channelId, ownerId]) => {
          channelsChecked++;

          // Find which guild this channel belongs to using our pre-populated map
          let channel = null;
          let foundGuild = null;

          for (const [guildId, channelsCache] of guildChannelsMap.entries()) {
            const ch = channelsCache.get(channelId);
            if (ch) {
              channel = ch;
              foundGuild = client.guilds.cache.get(guildId);
              break;
            }
          }

          // If channel doesn't exist in any guild, remove from tracking
          if (!channel) {
            client.tempChannels.delete(channelId);
            channelsRemoved++;
            logger.info(
              `Removed stale channel ${channelId} from tracking (doesn't exist in any guild)`
            );
            return;
          }

          // Track this user as having a valid channel
          usersWithChannels.add(ownerId);

          // If channel exists but is empty, remove from tracking and optionally delete
          if (channel.type === 2 && channel.members.size === 0) {
            // 2 = GUILD_VOICE
            // Check if this is a waiting room
            const isWaitingRoom = channel.name.toLowerCase().includes('waiting-room');

            if (isWaitingRoom) {
              // If this waiting room is empty and not already being tracked, add it to tracking
              if (!emptyWaitingRooms.has(channelId)) {
                logger.debug(
                  `Found empty waiting room ${channel.name} (${channelId}), tracking for 1-minute deletion`
                );
                emptyWaitingRooms.set(channelId, Date.now());
              }
              return; // Skip further processing for waiting rooms
            }

            // Remove from tracking immediately for non-waiting room channels
            client.tempChannels.delete(channelId);
            channelsRemoved++;

            // Delete the channel if requested
            if (deleteEmptyChannels) {
              try {
                // Get guild settings to check if this is a JOIN_TO_CREATE channel
                const guildSettings = await dataManager.loadGuildSettings();
                const guildId = channel.guild.id;
                const settings = guildSettings[guildId] || {};
                const joinChannelId = settings.joinChannelId || settings.JoinChannel;

                // Check if this is a JOIN_TO_CREATE channel or has "join" in the name
                const isJoinChannel = channelId === joinChannelId;
                const hasJoinInName = channel.name.toLowerCase().includes('join');

                if (isJoinChannel) {
                  logger.debug(
                    `Regular cleanup: Skipping JOIN_TO_CREATE channel ${channel.name} (${channelId})`
                  );
                } else if (hasJoinInName) {
                  logger.debug(
                    `Regular cleanup: Skipping channel with 'join' in name: ${channel.name} (${channelId})`
                  );
                } else {
                  // Only delete if it's not a JOIN_TO_CREATE channel and doesn't have "join" in the name
                  logger.info(`Deleting empty channel ${channel.name} (${channelId})`);

                  // Add to deletion promises instead of awaiting
                  deletionPromises.push(
                    channel
                      .delete(`Automatic cleanup: Channel empty`)
                      .then(() => {
                        channelsDeleted++;
                        logger.info(`Successfully deleted empty channel ${channelId}`);
                      })
                      .catch(error => {
                        logger.error(`Failed to delete channel ${channelId}: ${error.message}`);
                      })
                  );
                }
              } catch (error) {
                logger.error(`Error during channel deletion: ${error.message}`);
              }
            } else {
              logger.info(`Removed stale channel ${channelId} from tracking (empty)`);
            }
          }
          // Check if the owner is connected to any voice channel in any guild
          // This helps clean up orphaned channels when users switch guilds
          else if (channel.type === 2) {
            let ownerConnectedAnywhere = false;

            // Check if the owner is connected to any voice channel in any guild
            for (const guild of client.guilds.cache.values()) {
              try {
                // Use ownerId as a string for fetching the member
                const guildMember = await guild.members
                  .fetch({ user: ownerId.toString() })
                  .catch(() => null);
                if (guildMember && guildMember.voice.channelId) {
                  // Owner is connected to a voice channel somewhere
                  ownerConnectedAnywhere = true;
                  break;
                }
              } catch (error) {
                // Ignore errors when checking members
                continue;
              }
            }

            // If owner is not connected to any voice channel but the channel still has members,
            // it's likely an orphaned channel (owner switched guilds)
            if (!ownerConnectedAnywhere && channel.members.size > 0) {
              logger.info(
                `Owner ${ownerId} not connected to any voice channel but channel ${channelId} still has members. Keeping it tracked.`
              );

              // We'll keep tracking the channel since it still has members
              // This ensures it will be properly cleaned up when it becomes empty
              // We don't need to do anything here, just keep the channel in tracking
            }
          }
        })
      );
    }

    // Wait for all deletion promises to complete
    if (deletionPromises.length > 0) {
      await Promise.allSettled(deletionPromises);
    }

    // Now clean up waiting rooms for users whose channels were removed or don't exist
    if (client.userSettings.size > 0) {
      waitingRoomsDeleted = await cleanupOrphanedWaitingRooms(usersWithChannels);
    }

    // Save the updated tempChannels map - don't wait for it to complete
    if (channelsRemoved > 0) {
      saveTempChannels().catch(err => {
        logger.error(`Failed to save temp channels during cleanup: ${err.message}`);
      });

      logger.info(
        `Cleanup complete: Checked ${channelsChecked} channels, removed ${channelsRemoved} stale channels, deleted ${channelsDeleted} empty channels, deleted ${waitingRoomsDeleted} orphaned waiting rooms`
      );
    } else {
      logger.debug(
        `Cleanup complete: No channels needed removal, deleted ${waitingRoomsDeleted} orphaned waiting rooms`
      );
    }
  } catch (error) {
    logger.error(`Failed to clean up empty voice channels: ${error.message}`);
  }
}

/**
 * Check for empty waiting rooms and track them for deletion after 1 minute
 * @returns {number} - Number of waiting rooms deleted
 */
async function checkEmptyWaitingRooms() {
  let waitingRoomsDeleted = 0;
  const deletionPromises = [];
  const now = Date.now();

  try {
    // Check all waiting rooms that we're tracking
    for (const [waitingRoomId, timestamp] of emptyWaitingRooms.entries()) {
      // If the waiting room has been empty for more than 1 minute (60000ms), delete it
      if (now - timestamp >= 60000) {
        logger.info(`Waiting room ${waitingRoomId} has been empty for 1 minute, deleting it`);

        // Find the waiting room channel
        let waitingRoomChannel = null;
        let foundGuild = null;

        // Search for the channel in all guilds
        for (const guild of client.guilds.cache.values()) {
          try {
            waitingRoomChannel = await guild.channels.fetch(waitingRoomId).catch(() => null);
            if (waitingRoomChannel) {
              foundGuild = guild;
              break;
            }
          } catch (error) {
            continue;
          }
        }

        if (waitingRoomChannel) {
          // Find the user who owns this waiting room
          let ownerId = null;
          for (const [userId, settings] of client.userSettings.entries()) {
            if (settings.waitingRoomId === waitingRoomId) {
              ownerId = userId;
              break;
            }
          }

          // Delete the waiting room
          deletionPromises.push(
            waitingRoomChannel
              .delete(`Automatic cleanup: Waiting room empty for 1 minute`)
              .then(() => {
                waitingRoomsDeleted++;
                logger.info(`Successfully deleted empty waiting room ${waitingRoomId}`);

                // Update user settings if we found the owner
                if (ownerId) {
                  const settings = client.userSettings.get(ownerId);
                  if (settings) {
                    settings.hasWaitingRoom = false;
                    settings.waitingRoomId = null;
                    client.userSettings.set(ownerId, settings);
                    queueUserSettingsSave(ownerId);
                    logger.debug(
                      `Updated user settings for ${ownerId}: waiting room flags cleared`
                    );
                  }
                }

                return true;
              })
              .catch(error => {
                logger.error(`Failed to delete waiting room ${waitingRoomId}: ${error.message}`);
                return false;
              })
          );
        }

        // Remove from tracking regardless of whether we found the channel
        emptyWaitingRooms.delete(waitingRoomId);
      }
    }

    // Wait for all deletion operations to complete
    if (deletionPromises.length > 0) {
      await Promise.allSettled(deletionPromises);
    }

    return waitingRoomsDeleted;
  } catch (error) {
    logger.error(`Error checking empty waiting rooms: ${error.message}`);
    return 0;
  }
}

/**
 * Clean up orphaned waiting rooms
 * @param {Set} usersWithChannels - Set of user IDs who still have valid channels
 * @returns {number} - Number of waiting rooms deleted
 */
async function cleanupOrphanedWaitingRooms(usersWithChannels = new Set()) {
  let waitingRoomsDeleted = 0;
  const deletionPromises = [];
  const settingsToUpdate = [];

  try {
    logger.debug(`Checking for orphaned waiting rooms...`);

    // Create a map of all channels for faster lookup
    const allChannelsMap = new Map();

    // Pre-populate the map with all channels from all guilds
    for (const guild of client.guilds.cache.values()) {
      for (const [channelId, channel] of guild.channels.cache.entries()) {
        allChannelsMap.set(channelId, { channel, guild });
      }
    }

    // Find all users with waiting rooms who don't have active channels
    const orphanedWaitingRooms = [];

    for (const [userId, settings] of client.userSettings.entries()) {
      // Skip users who still have channels
      if (usersWithChannels.has(userId)) {
        continue;
      }

      // Check if user has a waiting room configured
      if (settings.hasWaitingRoom && settings.waitingRoomId) {
        // Check if the waiting room exists in our map
        const waitingRoomInfo = allChannelsMap.get(settings.waitingRoomId);

        if (waitingRoomInfo) {
          // Waiting room exists, add to deletion list
          orphanedWaitingRooms.push({
            userId,
            settings,
            waitingRoom: waitingRoomInfo.channel,
            waitingRoomId: settings.waitingRoomId,
          });
        } else {
          // Waiting room doesn't exist, just update settings
          settings.hasWaitingRoom = false;
          settings.waitingRoomId = null;
          settingsToUpdate.push(userId);
          client.userSettings.set(userId, settings);
          logger.debug(
            `User settings updated: waiting room flags cleared for user ${userId} (room not found)`
          );
        }
      }
    }

    // Process waiting room deletions in parallel
    if (orphanedWaitingRooms.length > 0) {
      logger.info(`Found ${orphanedWaitingRooms.length} orphaned waiting rooms to delete`);

      // Process in batches of 5 for better performance
      const batchSize = 5;
      for (let i = 0; i < orphanedWaitingRooms.length; i += batchSize) {
        const batch = orphanedWaitingRooms.slice(i, i + batchSize);

        // Process this batch in parallel
        const batchPromises = batch.map(({ userId, settings, waitingRoom, waitingRoomId }) => {
          logger.info(
            `Deleting orphaned waiting room ${waitingRoom.name} (${waitingRoomId}) for user ${userId}`
          );

          // Update settings immediately
          settings.hasWaitingRoom = false;
          settings.waitingRoomId = null;
          client.userSettings.set(userId, settings);
          settingsToUpdate.push(userId);

          // Delete the waiting room
          return waitingRoom
            .delete(`Automatic cleanup: Orphaned waiting room`)
            .then(() => {
              waitingRoomsDeleted++;
              logger.info(`Successfully deleted orphaned waiting room ${waitingRoomId}`);
              return true;
            })
            .catch(error => {
              logger.error(`Failed to delete waiting room ${waitingRoomId}: ${error.message}`);
              return false;
            });
        });

        // Wait for all deletions in this batch to complete
        await Promise.allSettled(batchPromises);
      }
    }

    // Save all updated user settings at once
    if (settingsToUpdate.length > 0) {
      // Use a single saveUserSettings call for all updated users
      const settingsObj = {};
      for (const userId of settingsToUpdate) {
        settingsObj[userId] = client.userSettings.get(userId);
      }

      // Save settings in the background
      dataManager.saveUserSettings(settingsObj).catch(error => {
        logger.error(`Failed to save updated user settings: ${error.message}`);
        // Fall back to individual saves if batch save fails
        for (const userId of settingsToUpdate) {
          global.saveUserSettings(userId);
        }
      });
    }

    if (waitingRoomsDeleted > 0) {
      logger.info(`Cleaned up ${waitingRoomsDeleted} orphaned waiting rooms`);
    } else {
      logger.debug(`No orphaned waiting rooms found`);
    }

    return waitingRoomsDeleted;
  } catch (error) {
    logger.error(`Failed to clean up orphaned waiting rooms: ${error.message}`);
    return 0;
  }
}

/**
 * Specifically checks MyVC categories for empty channels and deletes them immediately
 * This is a more aggressive cleanup that runs every 20 seconds
 * Also checks for empty waiting rooms and tracks them for deletion after 1 minute
 */
async function cleanupEmptyVoiceChannelsInCategories() {
  try {
    let channelsDeleted = 0;
    const deletionPromises = [];

    // First, check and delete waiting rooms that have been empty for 1 minute
    await checkEmptyWaitingRooms();

    // Load guild settings to get tempCategoryIds
    const guildSettings = await dataManager.loadGuildSettings();

    // Process each guild
    for (const guild of client.guilds.cache.values()) {
      try {
        // Get the temp category for this guild
        const guildId = guild.id;
        const settings = guildSettings[guildId] || {};
        const tempCategoryId = settings.tempCategoryId || settings.TempCategory;

        if (!tempCategoryId) {
          continue;
        }

        // Fetch all channels in this guild
        const guildChannels = guild.channels.cache;

        // Check all voice channels in this guild
        for (const [channelId, channel] of guildChannels.entries()) {
          // Only check voice channels
          if (channel.type !== 2) {
            continue;
          }

          // Check if this is a waiting room by name
          const isWaitingRoom = channel.name.toLowerCase().includes('waiting-room');

          // Handle waiting rooms
          if (isWaitingRoom && channel.members.size === 0) {
            // If this waiting room is empty and not already being tracked, add it to tracking
            if (!emptyWaitingRooms.has(channelId)) {
              logger.debug(
                `Found empty waiting room ${channel.name} (${channelId}), tracking for 1-minute deletion`
              );
              emptyWaitingRooms.set(channelId, Date.now());
            }
            // Skip further processing for waiting rooms
            continue;
          }

          // For non-waiting room channels in the temp category
          if (channel.parentId === tempCategoryId) {
            // Get the JOIN_TO_CREATE channel ID from guild settings
            const joinChannelId = settings.joinChannelId || settings.JoinChannel;

            // Check if this is a JOIN_TO_CREATE channel or has "join" in the name
            const isJoinChannel = channelId === joinChannelId;
            const hasJoinInName = channel.name.toLowerCase().includes('join');

            if (isJoinChannel) {
              logger.debug(
                `Category cleanup: Skipping JOIN_TO_CREATE channel ${channel.name} (${channelId})`
              );
            } else if (hasJoinInName) {
              logger.debug(
                `Category cleanup: Skipping channel with 'join' in name: ${channel.name} (${channelId})`
              );
            } else if (channel.members.size === 0) {
              // If the channel is empty and not a JOIN_TO_CREATE channel, delete it immediately
              logger.info(
                `Category cleanup: Deleting empty channel ${channel.name} (${channelId})`
              );

              // Remove from tracking
              client.tempChannels.delete(channelId);

              // Delete the channel
              deletionPromises.push(
                channel
                  .delete(`Automatic category cleanup: Channel empty`)
                  .then(() => {
                    channelsDeleted++;
                    logger.info(
                      `Category cleanup: Successfully deleted empty channel ${channelId}`
                    );
                  })
                  .catch(error => {
                    logger.error(
                      `Category cleanup: Failed to delete channel ${channelId}: ${error.message}`
                    );
                  })
              );
            }
          }
        }
      } catch (error) {
        logger.error(`Error cleaning up guild ${guild.id}: ${error.message}`);
      }
    }

    // Wait for all deletion operations to complete
    await Promise.allSettled(deletionPromises);

    if (channelsDeleted > 0) {
      logger.info(`Category cleanup complete: Deleted ${channelsDeleted} empty channels`);

      // Save the updated tempChannels map if we deleted any channels
      saveTempChannels().catch(err => {
        logger.error(`Failed to save temp channels during category cleanup: ${err.message}`);
      });
    }

    return channelsDeleted;
  } catch (error) {
    logger.error(`Error in category cleanup: ${error.message}`);
    return 0;
  }
}

/**
 * Clean up voice channels on bot startup
 * This ensures any empty channels left when the bot was offline are properly cleaned up
 */
async function startupCleanup() {
  logger.info('Starting initial cleanup of voice channels...');

  try {
    // Only fetch channels from MyVC categories
    logger.debug('Fetching only MyVC category channels from Discord API...');

    let fetchedChannels = 0;
    const fetchPromises = [];

    // Load guild settings to get tempCategoryIds
    const guildSettings = await dataManager.loadGuildSettings();

    for (const guild of client.guilds.cache.values()) {
      // Only fetch from guilds that have a MyVC category configured
      if (guildSettings[guild.id] && guildSettings[guild.id].tempCategoryId) {
        const categoryId = guildSettings[guild.id].tempCategoryId;

        fetchPromises.push(
          // Only fetch the specific category instead of all guild channels
          guild.channels
            .fetch(categoryId)
            .then(async category => {
              if (category) {
                // Fetch only the channels that belong to this category
                // Using filter on guild channels instead of category.children.fetch
                const childChannels = await guild.channels
                  .fetch()
                  .then(channels => channels.filter(c => c.parentId === categoryId));

                fetchedChannels += childChannels.size;
                logger.debug(
                  `Fetched ${childChannels.size} channels from MyVC category in guild ${guild.name} (${guild.id})`
                );
              } else {
                logger.warn(
                  `MyVC category ${categoryId} not found in guild ${guild.name} (${guild.id})`
                );
              }
            })
            .catch(async error => {
              if (error.message === 'Unknown Channel') {
                logger.warn(
                  `MyVC category ${categoryId} no longer exists in guild ${guild.name} (${guild.id}). Cleaning up guild settings.`
                );

                // Clean up the invalid category ID from guild settings
                try {
                  await dataManager.updateGuildSettings(guild.id, {
                    tempCategoryId: null,
                    joinChannelId: null, // Also clear join channel as it was likely in the deleted category
                  });
                  logger.info(`Cleaned up invalid category ID for guild ${guild.id}`);
                } catch (cleanupError) {
                  logger.error(`Failed to clean up guild settings for ${guild.id}:`, cleanupError);
                }
              } else {
                logger.error(`Failed to fetch MyVC category for guild ${guild.id}: ${error.message}`);
              }
            })
        );
      } else {
        logger.debug(`Guild ${guild.name} (${guild.id}) has no MyVC category configured, skipping`);
      }
    }

    // Wait for all category fetches to complete
    await Promise.all(fetchPromises);
    logger.info(
      `Fetched a total of ${fetchedChannels} channels from MyVC categories across ${client.guilds.cache.size} guilds`
    );

    // Now perform cleanup with the updated cache
    await cleanupEmptyVoiceChannels(true); // Delete empty channels
  } catch (error) {
    logger.error(`Error during startup cleanup: ${error.message}`);

    // Still try to run the cleanup even if fetching fails
    await cleanupEmptyVoiceChannels(true);
  }
}

/**
 * Load temporary channels from file
 */
async function loadTempChannels() {
  try {
    // First, clean up stale channels from database
    logger.debug('Cleaning up stale channels from database before loading...');
    const removedCount = await dataManager.cleanupStaleTempChannels();
    if (removedCount > 0) {
      logger.info(`Removed ${removedCount} stale channels from database during startup`);
    }

    // Use dataManager to load temp channels
    const channelsObj = await dataManager.loadTempChannels();

    // Convert object to Map
    let loadedCount = 0;
    for (const [channelId, userId] of Object.entries(channelsObj) as any) {
      client.tempChannels.set(channelId, userId);
      loadedCount++;
    }

    logger.info(`Loaded ${loadedCount} temporary channels from storage`);
  } catch (error) {
    logger.error('Failed to load temporary channels:', error);
  }
}

/**
 * Save temporary channels to file
 */
async function saveTempChannels() {
  try {
    // Use dataManager to save temp channels
    const success = await dataManager.saveTempChannels(client.tempChannels);

    if (success) {
      logger.debug(`Successfully saved ${client.tempChannels.size} temp channels`);
    } else {
      logger.error('Failed to save temp channels');
    }

    return success;
  } catch (error) {
    logger.error('Failed to save temp channels:', error);
    return false;
  }
}

// Make createEmbed available globally
global.createEmbed = createEmbed;

// Make stats counters available globally
global.incrementMessageCount = () => totalMessagesProcessed++;
global.incrementCommandCount = () => totalCommandsUsed++;
global.getStats = () => ({
  totalMessagesProcessed,
  totalCommandsUsed,
  totalSaveOperations,
  successfulSaveOperations,
  startTime,
});

// Determine file extension based on whether we're running in dev or production
const fileExtension = __filename.endsWith('.ts') ? '.ts' : '.js';

// Register command files
const commandsPath = path.join(__dirname, 'commands');
// Make sure directory exists before trying to read it
if (fs.existsSync(commandsPath)) {
  const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith(fileExtension));

  // Log command loading
  logger.info(`Found ${commandFiles.length} command files`);

  for (const file of commandFiles) {
    const filePath = path.join(commandsPath, file);
    try {
      const command = require(filePath);

      if ('data' in command && 'execute' in command) {
        client.commands.set(command.data.name, command);
        logger.debug(`Registered command: ${command.data.name}`);
      } else {
        logger.warn(
          `The command at ${filePath} is missing a required "data" or "execute" property.`
        );
      }
    } catch (error) {
      logger.error(`Error loading command from ${file}:`, error);
    }
  }
} else {
  logger.warn(`Commands directory not found: ${commandsPath}`);
}

// Register text command files
const textCommandsPath = path.join(__dirname, 'textCommands');
if (fs.existsSync(textCommandsPath)) {
  const textCommandFiles = fs
    .readdirSync(textCommandsPath)
    .filter(file => file.endsWith(fileExtension));

  // Log command loading
  logger.info(`Found ${textCommandFiles.length} text command files`);

  for (const file of textCommandFiles) {
    const filePath = path.join(textCommandsPath, file);
    try {
      const command = require(filePath);

      if ('name' in command && 'execute' in command) {
        client.textCommands.set(command.name, command);
        logger.debug(`Registered text command: ${command.name}`);
      } else {
        logger.warn(
          `The text command at ${filePath} is missing a required "name" or "execute" property.`
        );
      }
    } catch (error) {
      logger.error(`Error loading text command from ${file}:`, error);
    }
  }
}

// Add these functions before the event registration
/**
 * Load a command into the bot
 * @param {string} commandName - Name of the command to load
 * @returns {Object} Result with success status and message
 */
function loadCommand(commandName) {
  try {
    const fileExt = __filename.endsWith('.ts') ? '.ts' : '.js';
    const commandPath = path.join(__dirname, 'textCommands', `${commandName}${fileExt}`);

    // Check if file exists
    if (!fs.existsSync(commandPath)) {
      return { success: false, message: `Command file ${commandName}${fileExt} does not exist` };
    }

    // Delete from cache if already loaded
    delete require.cache[require.resolve(commandPath)];

    // Load the command
    const command = require(commandPath);

    // Check required properties
    if (!command.name || !command.execute) {
      return { success: false, message: `Command ${commandName} is missing required properties` };
    }

    // Add to collection
    client.textCommands.set(command.name, command);

    return { success: true, message: `Command ${commandName} was loaded successfully` };
  } catch (error) {
    logger.error(`Error loading command ${commandName}:`, error);
    return { success: false, message: `Error loading command: ${error.message}` };
  }
}

/**
 * Unload a command from the bot
 * @param {string} commandName - Name of the command to unload
 * @returns {Object} Result with success status and message
 */
function unloadCommand(commandName) {
  try {
    // Check if command exists
    if (!client.textCommands.has(commandName)) {
      return { success: false, message: `Command ${commandName} is not loaded` };
    }

    // Find command file path
    const fileExt = __filename.endsWith('.ts') ? '.ts' : '.js';
    const commandPath = path.join(__dirname, 'textCommands', `${commandName}${fileExt}`);

    // Delete from cache
    delete require.cache[require.resolve(commandPath)];

    // Remove from collection
    client.textCommands.delete(commandName);

    return { success: true, message: `Command ${commandName} was unloaded successfully` };
  } catch (error) {
    logger.error(`Error unloading command ${commandName}:`, error);
    return { success: false, message: `Error unloading command: ${error.message}` };
  }
}

/**
 * Reload a command
 * @param {string} commandName - Name of the command to reload
 * @returns {Object} Result with success status and message
 */
function reloadCommand(commandName) {
  // First unload
  const unloadResult = unloadCommand(commandName);

  if (!unloadResult.success) {
    return unloadResult;
  }

  // Then load
  return loadCommand(commandName);
}

// Make these functions available globally
global.loadCommand = loadCommand;
global.unloadCommand = unloadCommand;
global.reloadCommand = reloadCommand;
global.loadTempChannels = loadTempChannels;
global.saveTempChannels = saveTempChannels; // Make saveTempChannels available globally
global.startupCleanup = startupCleanup;
global.cleanupEmptyVoiceChannelsInCategories = cleanupEmptyVoiceChannelsInCategories;
global.emptyWaitingRooms = emptyWaitingRooms; // Make the emptyWaitingRooms Map available globally

// Register event files
const eventsPath = path.join(__dirname, 'events');
// Make sure directory exists before trying to read it
if (fs.existsSync(eventsPath)) {
  const eventFiles = fs.readdirSync(eventsPath).filter(file => file.endsWith(fileExtension));

  // Log event loading
  logger.info(`Found ${eventFiles.length} event files`);

  for (const file of eventFiles) {
    const filePath = path.join(eventsPath, file);
    try {
      const event = require(filePath);

      if (event.once) {
        client.once(event.name, (...args) => event.execute(...args, client));
        logger.debug(`Registered one-time event: ${event.name}`);
      } else {
        client.on(event.name, (...args) => event.execute(...args, client));
        logger.debug(`Registered event: ${event.name}`);
      }
    } catch (error) {
      logger.error(`Error loading event from ${file}:`, error);
    }
  }
} else {
  logger.warn(`Events directory not found: ${eventsPath}`);
}

// Initialize intervals
let userSettingsSaveInterval;
let cleanupInterval;
let categoryCleanupInterval;
let statsInterval;

// Start periodic tasks when bot is ready
client.once(Events.ClientReady, async () => {
  // If we're running in sharded mode, log that information
  if (client.shard) {
    logger.info(`Shard ${client.shard.ids.join('/')} logged in as ${client.user.tag}`);
    logger.info(`This shard is responsible for ${client.guilds.cache.size} guilds`);
  } else {
    logger.info(`Logged in as ${client.user.tag}`);
    logger.info(`Ready to serve in ${client.guilds.cache.size} guilds`);
  }

  // Stagger startup operations to improve initial response time
  // First load essential settings immediately
  await loadUserSettings();

  // Defer non-critical startup operations
  setTimeout(async () => {
    // Clean up invalid guild settings first
    logger.debug('Cleaning up invalid guild settings...');
    const cleanedGuilds = await dataManager.cleanupInvalidGuildSettings();
    if (cleanedGuilds > 0) {
      logger.info(`Cleaned up invalid settings for ${cleanedGuilds} guilds during startup`);
    }

    // Load existing temporary channels (this now includes database cleanup)
    await loadTempChannels();
    logger.info('Loaded temporary channels data');

    // Run startup cleanup with a slight delay to prioritize responsiveness
    // This will now only clean up in-memory data since database cleanup was done during loading
    setTimeout(async () => {
      await startupCleanup();
      logger.info('Startup cleanup completed');
    }, 5000);
  }, 3000);

  // Set up regular saving of user settings
  userSettingsSaveInterval = setInterval(() => {
    if (client.userSettings.size > 0) {
      queueUserSettingsSave();
    }
  }, INTERVALS.SAVE_DATA);

  // Set up regular cleanup of empty voice channels
  cleanupInterval = setInterval(async () => {
    await cleanupEmptyVoiceChannels(true);
  }, INTERVALS.CLEANUP);

  // Set up dedicated cleanup for MyVC categories (runs every 20 seconds)
  categoryCleanupInterval = setInterval(async () => {
    await cleanupEmptyVoiceChannelsInCategories();
  }, INTERVALS.CATEGORY_CLEANUP);

  // Set up regular saving of bot stats
  statsInterval = setInterval(async () => {
    await saveBotStats();
  }, INTERVALS.STATS);

  // Log performance metrics every hour
  setInterval(() => {
    const uptime = Date.now() - startTime;
    const memoryUsage = process.memoryUsage();

    logger.info(`Bot uptime: ${formatUptime(uptime)}`);
    logger.info(
      `Memory usage: ${formatBytes(memoryUsage.rss)} (RSS), ${formatBytes(memoryUsage.heapUsed)} (Heap)`
    );
    logger.info(`Tracking ${client.tempChannels.size} temporary channels`);
    logger.info(`Storing settings for ${client.userSettings.size} users`);
    logger.info(`Messages processed: ${totalMessagesProcessed}, Commands used: ${totalCommandsUsed}`);
    logger.info(
      `Total save operations: ${totalSaveOperations}, successful: ${successfulSaveOperations}`
    );
  }, INTERVALS.PERFORMANCE_LOG);
});

// Handle process shutdown
process.on('SIGINT', handleShutdown);
process.on('SIGTERM', handleShutdown);

/**
 * Clean shutdown handler
 */
async function handleShutdown() {
  try {
    logger.info(
      `Shutting down${client.shard ? ` shard ${client.shard.ids.join('/')}` : ''}, cleaning up...`
    );

    // Clear intervals to prevent further operations during shutdown
    clearInterval(userSettingsSaveInterval);
    clearInterval(cleanupInterval);
    clearInterval(categoryCleanupInterval);
    clearInterval(statsInterval);

    // Clear any pending operations
    saveQueue.clear();

    // Destroy client first to prevent new operations
    if (client) {
      logger.info('Destroying Discord client...');
      await client.destroy();
      logger.info('Discord client destroyed');
    }

    // Then clean up data manager (this will handle MongoDB disconnection)
    logger.info('Running data manager cleanup...');
    await dataManager.cleanup();

    // Exit cleanly
    logger.info('Shutdown completed successfully');

    // In shard mode, let the parent process handle the exit
    if (!client.shard) {
      process.exit(0);
    }
  } catch (error) {
    logger.error('Error during shutdown:', error);

    // In shard mode, let the parent process handle the exit
    if (!client.shard) {
      process.exit(1);
    }
  }
}

// Login to Discord with bot token
const startBot = async () => {
  try {
    // Ensure Database is connected BEFORE logging in to Discord
    logger.info('Connecting to database...');
    await dataManager.connectToMongoDB(); // Wait for connection
    logger.info('Database connection successful.');

    // Login to Discord
    logger.info(`Logging in to Discord${isSharded ? ' (Shard Mode)' : ''}...`);
    await client.login(process.env.TOKEN);
  } catch (error) {
    logger.error('Failed to start bot:', error);
    process.exit(1);
  }
};

// Start the bot
startBot();

// Handle uncaught exceptions
process.on('uncaughtException', error => {
  logger.error('Uncaught Exception:', error);

  // Only exit for severe errors, not for Discord API errors that can be recovered from
  if (!(error.name === 'DiscordAPIError')) {
    handleShutdown();
  }
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason: any, promise) => {
  logger.error('Unhandled Rejection at:', promise);
  logger.error('Rejection reason:', reason);

  // Only exit for severe errors, not for Discord API errors that can be recovered from
  if (reason && !(reason.name === 'DiscordAPIError')) {
    logger.error('Shutting down due to unhandled rejection...');
    handleShutdown();
  }
});

// Expose methods for inter-shard communication
if (client.shard) {
  // Methods that can be called by the Sharding Manager via broadcastEval
  global.getShardInfo = () => {
    return {
      id: client.shard?.ids[0],
      guildCount: client.guilds.cache.size,
      tempChannelCount: client.tempChannels?.size || 0,
      userSettingsCount: client.userSettings?.size || 0,
      uptime: client.uptime,
      memoryUsage: process.memoryUsage(),
    };
  };
}
